@extends('layouts.app')

@section('title', 'Kelola Event UKM')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Kelola Event UKM</h1>
            <p class="text-gray-600">Kelola event untuk UKM yang Anda pimpin</p>
        </div>
        <a href="{{ route('ketua-ukm.events.create') }}"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-plus mr-2"></i>Tambah Event
        </a>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-calendar text-blue-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total Event</p>
                    <p class="text-lg font-semibold text-gray-900">{{ $events->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-check-circle text-green-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Published</p>
                    <p class="text-lg font-semibold text-gray-900">{{ $events->where('status', 'published')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-clock text-yellow-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Draft</p>
                    <p class="text-lg font-semibold text-gray-900">{{ $events->where('status', 'draft')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fas fa-clock text-purple-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Mendatang</p>
                    <p class="text-lg font-semibold text-gray-900">{{ $events->where('start_datetime', '>', now())->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- UKM Filter -->
    @if($leadingUkms->count() > 1)
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700">Filter UKM:</label>
            <select id="ukm-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Semua UKM</option>
                @foreach($leadingUkms as $ukm)
                    <option value="{{ $ukm->id }}">{{ $ukm->name }}</option>
                @endforeach
            </select>
        </div>
    </div>
    @endif

    <!-- Events Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UKM</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peserta</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approval</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($events as $event)
                    <tr class="hover:bg-gray-50" data-ukm-id="{{ $event->ukm_id }}">
                        <td class="px-6 py-4">
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ $event->title }}</div>
                                <div class="text-sm text-gray-500">{{ ucfirst($event->type) }}</div>
                                <div class="text-xs text-gray-400">{{ $event->location }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $event->ukm->name }}</div>
                            <div class="text-xs text-gray-500">{{ ucfirst($event->ukm->category) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $event->start_datetime->format('d M Y') }}</div>
                            <div class="text-xs text-gray-500">{{ $event->start_datetime->format('H:i') }} - {{ $event->end_datetime->format('H:i') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {{ $event->current_participants ?? 0 }}/{{ $event->max_participants ?? '∞' }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($event->status === 'published')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Published
                                </span>
                            @elseif($event->status === 'draft')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>Draft
                                </span>
                            @elseif($event->status === 'ongoing')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-play mr-1"></i>Ongoing
                                </span>
                            @elseif($event->status === 'completed')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-flag-checkered mr-1"></i>Completed
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-ban mr-1"></i>Cancelled
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($event->approval_status === 'approved')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Disetujui
                                </span>
                            @elseif($event->approval_status === 'rejected')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times mr-1"></i>Ditolak
                                </span>
                                @if($event->rejection_reason)
                                    <div class="text-xs text-gray-500 mt-1" title="{{ $event->rejection_reason }}">
                                        {{ Str::limit($event->rejection_reason, 30) }}
                                    </div>
                                @endif
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>Pending
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-3">
                                <!-- Regular Actions -->
                                <a href="{{ route('ketua-ukm.events.show', $event) }}"
                                   class="text-blue-600 hover:text-blue-900">
                                    Lihat
                                </a>

                                <a href="{{ route('ketua-ukm.events.registrations', $event) }}"
                                   class="text-green-600 hover:text-green-900"
                                   title="Kelola Pendaftaran">
                                    <i class="fas fa-user-check mr-1"></i>Pendaftar
                                </a>

                                @if($event->end_datetime < now())
                                    <a href="{{ route('ketua-ukm.events.attendances', $event) }}"
                                       class="text-purple-600 hover:text-purple-900"
                                       title="Verifikasi Absensi">
                                        <i class="fas fa-clipboard-check mr-1"></i>Absensi
                                    </a>
                                @endif

                                @if($event->status !== 'published')
                                    <a href="{{ route('ketua-ukm.events.edit', $event) }}"
                                       class="text-indigo-600 hover:text-indigo-900">
                                        Edit
                                    </a>
                                @endif

                                @if($event->status !== 'published')
                                    <form action="{{ route('ketua-ukm.events.destroy', $event) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                onclick="return confirm('Yakin ingin menghapus event {{ $event->title }}?')">
                                            Hapus
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-calendar text-4xl mb-4"></i>
                                <p class="text-lg font-medium">Belum ada event</p>
                                <p class="text-sm">Buat event pertama untuk UKM Anda.</p>
                                <a href="{{ route('ketua-ukm.events.create') }}"
                                   class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-plus mr-1"></i>Tambah Event
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($events->hasPages())
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            {{ $events->links() }}
        </div>
        @endif
    </div>
</div>

<script>
// UKM Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const ukmFilter = document.getElementById('ukm-filter');
    if (ukmFilter) {
        ukmFilter.addEventListener('change', function() {
            const selectedUkmId = this.value;
            const rows = document.querySelectorAll('tbody tr[data-ukm-id]');

            rows.forEach(row => {
                if (selectedUkmId === '' || row.dataset.ukmId === selectedUkmId) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});
</script>
@endsection
