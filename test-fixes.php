<?php

// Test script to verify all fixes are working
require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Ukm;
use App\Models\Event;

echo "Testing database connections and queries...\n";

try {
    // Test 1: User model queries
    echo "1. Testing User model...\n";
    $userCount = User::where('users.status', 'active')->count();
    echo "   Active users: $userCount\n";
    
    // Test 2: UKM model queries
    echo "2. Testing UKM model...\n";
    $ukmCount = Ukm::active()->count();
    echo "   Active UKMs: $ukmCount\n";
    
    // Test 3: Event model queries
    echo "3. Testing Event model...\n";
    $eventCount = Event::published()->count();
    echo "   Published events: $eventCount\n";
    
    // Test 4: UKM membership queries
    echo "4. Testing UKM membership queries...\n";
    $user = User::first();
    if ($user) {
        $membershipCount = $user->ukms()->where('ukm_members.status', 'active')->count();
        echo "   User memberships: $membershipCount\n";
    }
    
    // Test 5: UKM members queries
    echo "5. Testing UKM members queries...\n";
    $ukm = Ukm::first();
    if ($ukm) {
        $membersCount = $ukm->activeMembers()->count();
        echo "   UKM active members: $membersCount\n";
    }
    
    echo "\nAll tests passed! ✅\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "❌ Tests failed\n";
}
