<?php

namespace App\Http\Controllers;

use App\Models\Ukm;
use App\Models\Event;
use App\Models\User;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\NotificationService;

class KetuaUkmController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->isKetuaUkm()) {
                return redirect()->route('dashboard')->with('error', 'Anda tidak memiliki akses ketua UKM.');
            }
            return $next($request);
        });
    }

    /**
     * Show ketua UKM dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        $leadingUkms = $user->getLeadingUkms();

        // Get statistics for UKMs that this user leads
        $stats = [
            'total_ukms' => $leadingUkms->count(),
            'total_members' => 0,
            'total_events' => 0,
            'upcoming_events' => 0,
        ];

        // Calculate pending members count for notification badge
        $pendingCount = 0;
        foreach ($leadingUkms as $ukm) {
            $stats['total_members'] += $ukm->activeMembers()->count();
            $stats['total_events'] += $ukm->events()->count();
            $stats['upcoming_events'] += $ukm->events()->where('start_datetime', '>', now())->count();
            $pendingCount += $ukm->members()->wherePivot('status', 'pending')->count();
        }

        return view('ketua-ukm.dashboard', compact('leadingUkms', 'stats', 'pendingCount'));
    }

    /**
     * Show UKM management page.
     */
    public function manageUkm($id)
    {
        $ukm = Ukm::findOrFail($id);

        // Check if current user is the leader of this UKM
        if ($ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda tidak memiliki akses untuk mengelola UKM ini.');
        }

        $members = $ukm->activeMembers()->get();
        $events = $ukm->events()->orderBy('start_datetime', 'desc')->get();

        return view('ketua-ukm.manage-ukm', compact('ukm', 'members', 'events'));
    }

    /**
     * Show form to edit UKM.
     */
    public function editUkm($id)
    {
        $ukm = Ukm::findOrFail($id);

        // Check if current user is the leader of this UKM
        if ($ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda tidak memiliki akses untuk mengedit UKM ini.');
        }

        return view('ketua-ukm.edit-ukm', compact('ukm'));
    }

    /**
     * Update UKM information.
     */
    public function updateUkm(Request $request, $id)
    {
        $ukm = Ukm::findOrFail($id);

        // Check if current user is the leader of this UKM
        if ($ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda tidak memiliki akses untuk mengedit UKM ini.');
        }

        $request->validate([
            'description' => 'required|string',
            'vision' => 'nullable|string',
            'mission' => 'nullable|string',
            'meeting_schedule' => 'nullable|string',
            'meeting_location' => 'nullable|string',
            'achievements' => 'nullable|string',
            'organization_structure' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $contactInfo = [];
        if ($request->filled('contact_info.email')) $contactInfo['email'] = $request->input('contact_info.email');
        if ($request->filled('contact_info.phone')) $contactInfo['phone'] = $request->input('contact_info.phone');
        if ($request->filled('contact_info.instagram')) $contactInfo['instagram'] = $request->input('contact_info.instagram');
        if ($request->filled('contact_info.website')) $contactInfo['website'] = $request->input('contact_info.website');

        // Handle organization structure upload
        $organizationStructurePath = $ukm->organization_structure; // Keep existing by default
        if ($request->hasFile('organization_structure')) {
            // Delete old organization structure if exists
            if ($ukm->organization_structure && Storage::disk('public')->exists($ukm->organization_structure)) {
                Storage::disk('public')->delete($ukm->organization_structure);
            }

            // Store new organization structure
            $organizationStructurePath = $request->file('organization_structure')->store('ukms/organization_structures', 'public');
        }

        $ukm->update([
            'description' => $request->description,
            'vision' => $request->vision,
            'mission' => $request->mission,
            'meeting_schedule' => $request->meeting_schedule,
            'meeting_location' => $request->meeting_location,
            'achievements' => $request->achievements,
            'organization_structure' => $organizationStructurePath,
            'contact_info' => json_encode($contactInfo),
        ]);

        return redirect()->route('ketua-ukm.manage', $ukm->id)->with('success', 'Informasi UKM berhasil diperbarui.');
    }

    /**
     * Show events for ketua UKM.
     */
    public function events()
    {
        $user = Auth::user();
        $leadingUkms = $user->ledUkms;

        if ($leadingUkms->count() === 0) {
            return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda belum ditugaskan untuk memimpin UKM manapun.');
        }

        // Get all events from UKMs that this user leads
        $events = Event::whereIn('ukm_id', $leadingUkms->pluck('id'))
                      ->with(['ukm'])
                      ->orderBy('start_datetime', 'desc')
                      ->paginate(15);

        return view('ketua-ukm.events.index', compact('events', 'leadingUkms'));
    }

    /**
     * Show form to create event.
     */
    public function createEvent($ukmId = null)
    {
        $user = Auth::user();
        $leadingUkms = $user->ledUkms;

        if ($leadingUkms->count() === 0) {
            return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda belum ditugaskan untuk memimpin UKM manapun.');
        }

        // If specific UKM ID provided, validate access
        if ($ukmId) {
            $ukm = Ukm::findOrFail($ukmId);
            if ($ukm->leader_id !== Auth::id()) {
                return redirect()->route('ketua-ukm.dashboard')->with('error', 'Anda tidak memiliki akses untuk membuat event untuk UKM ini.');
            }
        } else {
            $ukm = null;
        }

        $types = ['workshop', 'seminar', 'competition', 'meeting', 'social', 'other'];

        return view('ketua-ukm.events.create', compact('leadingUkms', 'ukm', 'types'));
    }

    /**
     * Store new event.
     */
    public function storeEvent(Request $request)
    {
        $request->validate([
            'ukm_id' => 'required|exists:ukms,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_datetime' => 'required|date',
            'end_datetime' => 'required|date|after:start_datetime',
            'location' => 'required|string|max:255',
            'max_participants' => 'nullable|integer|min:1',
            'type' => 'required|in:workshop,seminar,competition,meeting,social,other',
            'registration_open' => 'boolean',
            'poster' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'proposal_file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'rab_file' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx|max:10240',
            'certificate_template' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
        ]);

        $ukm = Ukm::findOrFail($request->ukm_id);

        // Check if current user is the leader of this UKM
        if ($ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk membuat event untuk UKM ini.');
        }

        // Handle file uploads
        $posterPath = null;
        $proposalPath = null;
        $rabPath = null;
        $certificatePath = null;

        if ($request->hasFile('poster')) {
            $posterPath = $request->file('poster')->store('events/posters', 'public');
        }

        if ($request->hasFile('proposal_file')) {
            $proposalPath = $request->file('proposal_file')->store('events/proposals', 'public');
        }

        if ($request->hasFile('rab_file')) {
            $rabPath = $request->file('rab_file')->store('events/rab', 'public');
        }

        if ($request->hasFile('certificate_template')) {
            $certificatePath = $request->file('certificate_template')->store('events/certificates', 'public');
        }

        Event::create([
            'title' => $request->title,
            'slug' => \Illuminate\Support\Str::slug($request->title),
            'description' => $request->description,
            'start_datetime' => $request->start_datetime,
            'end_datetime' => $request->end_datetime,
            'location' => $request->location,
            'max_participants' => $request->max_participants,
            'type' => $request->type,
            'ukm_id' => $ukm->id,
            'status' => 'draft',
            'registration_open' => $request->has('registration_open'),
            'poster' => $posterPath,
            'proposal_file' => $proposalPath,
            'rab_file' => $rabPath,
            'certificate_template' => $certificatePath,
        ]);

        return redirect()->route('ketua-ukm.events')->with('success', 'Event berhasil dibuat.');
    }

    /**
     * Show event detail.
     */
    public function showEvent(Event $event)
    {
        // Check if current user is the leader of this UKM
        if ($event->ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk melihat event ini.');
        }

        $event->load(['ukm', 'registrations.user']);

        return view('ketua-ukm.events.show', compact('event'));
    }

    /**
     * Show form to edit event.
     */
    public function editEvent(Event $event)
    {
        // Check if current user is the leader of this UKM
        if ($event->ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk mengedit event ini.');
        }

        $user = Auth::user();
        $leadingUkms = $user->ledUkms;
        $types = ['workshop', 'seminar', 'competition', 'meeting', 'social', 'other'];

        return view('ketua-ukm.events.edit', compact('event', 'leadingUkms', 'types'));
    }

    /**
     * Update event.
     */
    public function updateEvent(Request $request, Event $event)
    {
        // Check if current user is the leader of this UKM
        if ($event->ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk mengedit event ini.');
        }

        $request->validate([
            'ukm_id' => 'required|exists:ukms,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_datetime' => 'required|date', // Removed after:now for testing
            'end_datetime' => 'required|date|after:start_datetime',
            'location' => 'required|string|max:255',
            'max_participants' => 'nullable|integer|min:1',
            'type' => 'required|in:workshop,seminar,competition,meeting,social,other',
            'registration_open' => 'boolean',
        ]);

        // Verify UKM access
        $ukm = Ukm::findOrFail($request->ukm_id);
        if ($ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk UKM ini.');
        }

        $event->update([
            'ukm_id' => $request->ukm_id,
            'title' => $request->title,
            'slug' => \Illuminate\Support\Str::slug($request->title),
            'description' => $request->description,
            'start_datetime' => $request->start_datetime,
            'end_datetime' => $request->end_datetime,
            'location' => $request->location,
            'max_participants' => $request->max_participants,
            'type' => $request->type,
            'registration_open' => $request->has('registration_open'),
        ]);

        return redirect()->route('ketua-ukm.events.show', $event)->with('success', 'Event berhasil diperbarui.');
    }

    /**
     * Delete event.
     */
    public function destroyEvent(Event $event)
    {
        // Check if current user is the leader of this UKM
        if ($event->ukm->leader_id !== Auth::id()) {
            return redirect()->route('ketua-ukm.events')->with('error', 'Anda tidak memiliki akses untuk menghapus event ini.');
        }

        // Only allow deletion if event is still draft or not published
        if ($event->status === 'published') {
            return redirect()->route('ketua-ukm.events')->with('error', 'Event yang sudah dipublikasikan tidak dapat dihapus.');
        }

        // Delete poster if exists
        if ($event->poster) {
            \Illuminate\Support\Facades\Storage::disk('public')->delete($event->poster);
        }

        $event->delete();

        return redirect()->route('ketua-ukm.events')->with('success', 'Event berhasil dihapus.');
    }

    /**
     * Show attendances for an event
     */
    public function showAttendances(Event $event)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk melihat absensi event ini.');
        }

        // Get attendances with filters
        $query = $event->attendances()->with(['user', 'registration']);

        // Apply search filter
        if (request('search')) {
            $search = request('search');
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (request('status')) {
            $query->where('status', request('status'));
        }

        // Apply verification filter
        if (request('verification')) {
            $query->where('verification_status', request('verification'));
        }

        $attendances = $query->orderBy('submitted_at', 'desc')
                            ->orderBy('created_at', 'desc')
                            ->paginate(20);

        return view('ketua-ukm.events.attendances', compact('event', 'attendances'));
    }

    /**
     * Verify attendance
     */
    public function verifyAttendance(Request $request, Event $event, $attendanceId)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk verifikasi absensi event ini.');
        }

        $attendance = $event->attendances()->findOrFail($attendanceId);

        $request->validate([
            'action' => 'required|in:verify,reject',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($request->action === 'verify') {
            $attendance->verify($user->id, $request->notes);
            $message = 'Absensi berhasil diverifikasi.';
        } else {
            $attendance->reject($user->id, $request->notes);
            $message = 'Absensi ditolak.';
        }

        return back()->with('success', $message);
    }

    /**
     * Show event registrations for approval
     */
    public function showEventRegistrations(Event $event)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk melihat pendaftaran event ini.');
        }

        // Get registrations with filters
        $query = $event->registrations()->with('user');

        // Apply search filter
        if (request('search')) {
            $search = request('search');
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (request('status')) {
            $query->where('status', request('status'));
        }

        $registrations = $query->orderBy('created_at', 'desc')
                              ->paginate(20);

        return view('ketua-ukm.events.registrations', compact('event', 'registrations'));
    }

    /**
     * Show registration details
     */
    public function showRegistrationDetails(Event $event, EventRegistration $registration)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk melihat detail pendaftaran ini.');
        }

        // Check if registration belongs to this event
        if ($registration->event_id !== $event->id) {
            abort(404, 'Pendaftaran tidak ditemukan untuk event ini.');
        }

        $registration->load(['user', 'event.ukm']);

        return view('ketua-ukm.events.registration-details', compact('event', 'registration'));
    }

    /**
     * Approve event registration
     */
    public function approveEventRegistration(Request $request, Event $event, $registrationId)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk menyetujui pendaftaran event ini.');
        }

        $registration = $event->registrations()->findOrFail($registrationId);

        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        // Update registration status
        $registration->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $user->id,
        ]);

        // Update event participant count
        $event->updateParticipantCount();

        // Send notification to user (if notification service exists)
        try {
            $notificationService = app(NotificationService::class);
            $notificationService->sendEventRegistrationApproved($registration->user, $event);
        } catch (\Exception $e) {
            // Log error but don't fail the approval
            Log::error('Failed to send notification: ' . $e->getMessage());
        }

        return back()->with('success', 'Pendaftaran berhasil disetujui.');
    }

    /**
     * Reject event registration
     */
    public function rejectEventRegistration(Request $request, Event $event, $registrationId)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk menolak pendaftaran event ini.');
        }

        $registration = $event->registrations()->findOrFail($registrationId);

        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        // Update registration status
        $registration->update([
            'status' => 'rejected',
            'rejection_reason' => $request->notes,
        ]);

        // Send notification to user (if notification service exists)
        try {
            $notificationService = app(NotificationService::class);
            $notificationService->sendEventRegistrationRejected($registration->user, $event, $request->notes);
        } catch (\Exception $e) {
            // Log error but don't fail the rejection
            Log::error('Failed to send notification: ' . $e->getMessage());
        }

        return back()->with('success', 'Pendaftaran berhasil ditolak.');
    }

    /**
     * Bulk approve event registrations
     */
    public function bulkApproveEventRegistrations(Request $request, Event $event)
    {
        $user = Auth::user();

        // Check if user is ketua UKM (simplified check for now)
        if ($user->role !== 'ketua_ukm') {
            abort(403, 'Anda tidak memiliki akses untuk menyetujui pendaftaran event ini.');
        }

        $request->validate([
            'registration_ids' => 'required|array',
            'registration_ids.*' => 'exists:event_registrations,id',
        ]);

        $registrationIds = $request->registration_ids;

        // Update all selected registrations
        $updated = $event->registrations()
                        ->whereIn('id', $registrationIds)
                        ->where('status', 'pending')
                        ->update([
                            'status' => 'approved',
                            'approved_at' => now(),
                            'approved_by' => $user->id,
                        ]);

        // Update event participant count
        $event->updateParticipantCount();

        // Send notifications (if service exists)
        try {
            $notificationService = app(NotificationService::class);
            $approvedRegistrations = $event->registrations()
                                          ->whereIn('id', $registrationIds)
                                          ->with('user')
                                          ->get();

            foreach ($approvedRegistrations as $registration) {
                $notificationService->sendEventRegistrationApproved($registration->user, $event);
            }
        } catch (\Exception $e) {
            // Log error but don't fail the approval
            Log::error('Failed to send bulk notifications: ' . $e->getMessage());
        }

        return back()->with('success', "Berhasil menyetujui $updated pendaftaran sekaligus.");
    }

    /**
     * Show pending members for approval.
     */
    public function pendingMembers()
    {
        $user = Auth::user();
        $ukm = Ukm::where('leader_id', $user->id)->first();

        if (!$ukm) {
            return redirect()->route('ketua-ukm.dashboard')
                           ->with('error', 'Anda tidak memiliki akses untuk mengelola anggota UKM.');
        }

        // Get only pending members
        $pendingMembers = $ukm->members()->wherePivot('status', 'pending')->get();

        return view('ketua-ukm.pending-members', compact('ukm', 'pendingMembers'));
    }

    /**
     * Show members management page.
     */
    public function members()
    {
        $user = Auth::user();

        // Debug: Log current user
        Log::info('Ketua UKM accessing members page', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role
        ]);

        // Get UKM that user leads
        $ukm = Ukm::where('leader_id', $user->id)->first();

        // Debug: Log UKM found
        Log::info('UKM lookup result', [
            'user_id' => $user->id,
            'ukm_found' => $ukm ? $ukm->name : 'None',
            'ukm_id' => $ukm ? $ukm->id : null
        ]);

        if (!$ukm) {
            return redirect()->route('ketua-ukm.dashboard')
                           ->with('error', 'Anda tidak memiliki akses untuk mengelola anggota UKM.');
        }

        // Get members by status
        $pendingMembers = $ukm->members()->wherePivot('status', 'pending')->get();
        $activeMembers = $ukm->members()->wherePivot('status', 'active')->get();
        $rejectedMembers = $ukm->members()->wherePivot('status', 'rejected')->get();

        // Debug: Log member counts
        Log::info('Member counts for UKM', [
            'ukm_id' => $ukm->id,
            'ukm_name' => $ukm->name,
            'pending_count' => $pendingMembers->count(),
            'active_count' => $activeMembers->count(),
            'rejected_count' => $rejectedMembers->count(),
            'pending_members' => $pendingMembers->pluck('name', 'id')->toArray(),
        ]);

        // Get recent statistics
        $recentlyApproved = $ukm->members()
            ->wherePivot('status', 'active')
            ->wherePivot('approved_at', '>=', now()->startOfMonth())
            ->get();

        $recentlyRejected = $ukm->members()
            ->wherePivot('status', 'rejected')
            ->wherePivot('rejected_at', '>=', now()->startOfMonth())
            ->get();

        return view('ketua-ukm.members', compact(
            'ukm', 'pendingMembers', 'activeMembers', 'rejectedMembers',
            'recentlyApproved', 'recentlyRejected'
        ));
    }

    /**
     * Approve member application.
     */
    public function approveMember($memberId)
    {
        $user = Auth::user();
        $ukm = Ukm::where('leader_id', $user->id)->first();

        if (!$ukm) {
            return back()->with('error', 'Anda tidak memiliki akses untuk mengelola anggota UKM.');
        }

        // Check if member exists and is pending
        $member = $ukm->members()->wherePivot('user_id', $memberId)->wherePivot('status', 'pending')->first();

        if (!$member) {
            return back()->with('error', 'Anggota tidak ditemukan atau sudah diproses.');
        }

        // Check if UKM has space
        if ($ukm->current_members >= $ukm->max_members) {
            return back()->with('error', 'UKM sudah mencapai kapasitas maksimal.');
        }

        // Update member status
        $ukm->members()->updateExistingPivot($memberId, [
            'status' => 'active',
            'approved_at' => now(),
            'approved_by' => $user->id,
            'joined_date' => now(),
        ]);

        // Update member count
        $ukm->increment('current_members');

        // Send notification to the approved member
        NotificationService::createUkmApplicationApproved($member, $ukm->name);

        return back()->with('success', 'Anggota berhasil diterima dan notifikasi telah dikirim.');
    }

    /**
     * Reject member application.
     */
    public function rejectMember($memberId)
    {
        $user = Auth::user();
        $ukm = Ukm::where('leader_id', $user->id)->first();

        if (!$ukm) {
            return back()->with('error', 'Anda tidak memiliki akses untuk mengelola anggota UKM.');
        }

        // Check if member exists and is pending
        $member = $ukm->members()->wherePivot('user_id', $memberId)->wherePivot('status', 'pending')->first();

        if (!$member) {
            return back()->with('error', 'Anggota tidak ditemukan atau sudah diproses.');
        }

        // Update member status - simplified without rejection_reason
        $ukm->members()->updateExistingPivot($memberId, [
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => $user->id,
        ]);

        // Send notification to the rejected member
        NotificationService::createUkmApplicationRejected($member, $ukm->name);

        return back()->with('success', 'Pendaftar berhasil ditolak dan notifikasi telah dikirim.');
    }

    /**
     * Remove member from UKM.
     */
    public function removeMember(Request $request, $memberId)
    {
        $request->validate([
            'removal_reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $ukm = Ukm::where('leader_id', $user->id)->first();

        if (!$ukm) {
            return back()->with('error', 'Anda tidak memiliki akses untuk mengelola anggota UKM.');
        }

        // Check if member exists and is active
        $member = $ukm->members()->wherePivot('user_id', $memberId)->wherePivot('status', 'active')->first();

        if (!$member) {
            return back()->with('error', 'Anggota tidak ditemukan atau tidak aktif.');
        }

        // Update member status
        $ukm->members()->updateExistingPivot($memberId, [
            'status' => 'inactive',
            'left_date' => now(),
            'notes' => $request->removal_reason,
        ]);

        // Update member count
        $ukm->decrement('current_members');

        return back()->with('success', 'Anggota berhasil dikeluarkan dari UKM.');
    }

    /**
     * Get member details for modal.
     */
    public function getMemberDetails($memberId)
    {
        $user = Auth::user();
        $ukm = Ukm::where('leader_id', $user->id)->first();

        if (!$ukm) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Get member details
        $member = $ukm->members()->where('ukm_members.user_id', $memberId)->first();

        if (!$member) {
            return response()->json(['error' => 'Member not found'], 404);
        }

        return response()->json([
            'previous_experience' => $member->pivot->previous_experience,
            'skills_interests' => $member->pivot->skills_interests,
            'reason_joining' => $member->pivot->reason_joining,
            'preferred_division' => $member->pivot->preferred_division,
            'cv_file' => $member->pivot->cv_file,
            'applied_at' => \App\Helpers\DateHelper::tableFormat($member->pivot->applied_at),
            'approved_at' => \App\Helpers\DateHelper::tableFormat($member->pivot->approved_at),
            'joined_date' => \App\Helpers\DateHelper::dateOnly($member->pivot->joined_date),
        ]);
    }

}
