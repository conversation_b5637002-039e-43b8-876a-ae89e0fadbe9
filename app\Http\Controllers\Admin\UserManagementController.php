<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class UserManagementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'student');

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('nim', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('major', 'like', "%{$search}%");
            });
        }

        if ($request->has('faculty') && $request->get('faculty') != '') {
            $query->where('faculty', $request->get('faculty'));
        }

        if ($request->has('status') && $request->get('status') != '') {
            $query->where('users.status', $request->get('status'));
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);

        $faculties = User::where('role', 'student')->distinct()->pluck('faculty');

        return view('admin.users.index', compact('users', 'faculties'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nim' => 'required|string|unique:users,nim',
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'gender' => 'required|in:male,female',
            'faculty' => 'required|string|max:255',
            'major' => 'required|string|max:255',
            'batch' => 'required|string|max:4',
        ]);

        User::create([
            'nim' => $request->nim,
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'gender' => $request->gender,
            'faculty' => $request->faculty,
            'major' => $request->major,
            'batch' => $request->batch,
            'role' => 'student',
            'status' => 'active',
        ]);

        return redirect()->route('admin.users.index')->with('success', 'Mahasiswa berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load('ukms');
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'nim' => 'required|string|unique:users,nim,' . $user->id,
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'gender' => 'required|in:male,female',
            'faculty' => 'required|string|max:255',
            'major' => 'required|string|max:255',
            'batch' => 'required|string|max:4',
            'status' => 'required|in:active,inactive,suspended',
            'role' => 'required|in:student,ketua_ukm,admin',
        ]);

        $updateData = $request->only([
            'nim', 'name', 'email', 'phone', 'gender',
            'faculty', 'major', 'batch', 'status', 'role'
        ]);

        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8|confirmed',
            ]);
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // Sync role with Spatie Permission
        $user->syncRoleWithSpatie();

        // Clear any cached user data
        if (function_exists('cache')) {
            cache()->forget('user_' . $user->id);
        }

        // Clear Spatie permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // If the current logged-in user is being updated, refresh their session
        if (Auth::check() && Auth::id() === $user->id) {
            Auth::setUser($user->fresh());
        }

        return redirect()->route('admin.dashboard')->with('success', 'Data mahasiswa berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if ($user->role === 'admin') {
            return redirect()->route('admin.users.index')->with('error', 'Tidak dapat menghapus akun admin.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')->with('success', 'Mahasiswa berhasil dihapus.');
    }

    /**
     * Activate user account
     */
    public function activate(User $user)
    {
        $user->update(['status' => 'active']);

        return redirect()->route('admin.users.index')->with('success', "Akun {$user->name} berhasil diaktifkan.");
    }

    /**
     * Suspend user account
     */
    public function suspend(User $user)
    {
        // Prevent suspending admin accounts
        if ($user->role === 'admin') {
            return redirect()->route('admin.users.index')->with('error', 'Tidak dapat suspend akun admin.');
        }

        $user->update(['status' => 'suspended']);

        return redirect()->route('admin.users.index')->with('success', "Akun {$user->name} berhasil disuspend.");
    }
}
