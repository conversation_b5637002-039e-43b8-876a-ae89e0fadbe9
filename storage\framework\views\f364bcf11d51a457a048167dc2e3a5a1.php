<?php $__env->startSection('title', 'Detail Kegiatan - ' . $event->title); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Detail Kegiatan</h1>
            <p class="text-gray-600"><?php echo e($event->title); ?></p>
        </div>
        <div class="flex space-x-2">
            <a href="<?php echo e(route('admin.events.edit', $event)); ?>" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>Edit
            </a>
            <a href="<?php echo e(route('admin.events.index')); ?>" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Event Info Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-start justify-between mb-4">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900"><?php echo e($event->title); ?></h2>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo e(ucfirst($event->type)); ?>

                            </span>
                            <?php if($event->status === 'published'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Published
                                </span>
                            <?php elseif($event->status === 'draft'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>Draft
                                </span>
                            <?php elseif($event->status === 'ongoing'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-play mr-1"></i>Ongoing
                                </span>
                            <?php elseif($event->status === 'completed'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-flag-checkered mr-1"></i>Completed
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-ban mr-1"></i>Cancelled
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <?php if($event->status === 'draft'): ?>
                        <form action="<?php echo e(route('admin.events.publish', $event)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                                    onclick="return confirm('Yakin ingin mempublikasikan event ini?')">
                                <i class="fas fa-check mr-1"></i>Publish
                            </button>
                        </form>
                        <?php endif; ?>
                        <?php if(in_array($event->status, ['draft', 'published'])): ?>
                        <form action="<?php echo e(route('admin.events.cancel', $event)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit" 
                                    class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                                    onclick="return confirm('Yakin ingin membatalkan event ini?')">
                                <i class="fas fa-ban mr-1"></i>Cancel
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($event->poster): ?>
                <div class="mb-6">
                    <img src="<?php echo e(asset('storage/' . $event->poster)); ?>" 
                         alt="<?php echo e($event->title); ?>" 
                         class="w-full h-64 object-cover rounded-lg">
                </div>
                <?php endif; ?>

                <div class="prose max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Deskripsi</h3>
                    <p class="text-gray-700 whitespace-pre-line"><?php echo e($event->description); ?></p>
                </div>

                <?php if($event->requirements): ?>
                <div class="mt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Persyaratan</h3>
                    <p class="text-gray-700 whitespace-pre-line"><?php echo e($event->requirements); ?></p>
                </div>
                <?php endif; ?>

                <?php if($event->notes): ?>
                <div class="mt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Catatan</h3>
                    <p class="text-gray-700 whitespace-pre-line"><?php echo e($event->notes); ?></p>
                </div>
                <?php endif; ?>
            </div>

            <!-- Registrations -->
            <?php if($event->registrations->count() > 0): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Pendaftar (<?php echo e($event->registrations->count()); ?>)</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peserta</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal Daftar</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__currentLoopData = $event->registrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full object-cover" 
                                                 src="<?php echo e($registration->user->avatar ? asset('storage/' . $registration->user->avatar) : asset('images/default-avatar.png')); ?>" 
                                                 alt="<?php echo e($registration->user->name); ?>">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($registration->user->name); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($registration->user->nim); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo e($registration->created_at->format('d M Y, H:i')); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if($registration->status === 'approved'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Approved
                                        </span>
                                    <?php elseif($registration->status === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Event Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informasi Kegiatan</h3>
                <div class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">UKM Penyelenggara</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($event->ukm->name); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Lokasi</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($event->location); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tanggal & Waktu</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($event->start_datetime->format('d M Y, H:i')); ?> - 
                            <?php echo e($event->end_datetime->format('H:i')); ?> WIB
                        </dd>
                    </div>
                    <?php if($event->registration_start || $event->registration_end): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Periode Pendaftaran</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php if($event->registration_start): ?>
                                <?php echo e($event->registration_start->format('d M Y, H:i')); ?>

                            <?php else: ?>
                                Segera
                            <?php endif; ?>
                            -
                            <?php if($event->registration_end): ?>
                                <?php echo e($event->registration_end->format('d M Y, H:i')); ?>

                            <?php else: ?>
                                Sampai event dimulai
                            <?php endif; ?>
                        </dd>
                    </div>
                    <?php endif; ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Peserta</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($event->current_participants); ?>/<?php echo e($event->max_participants ?? '∞'); ?>

                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Biaya</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php if($event->registration_fee > 0): ?>
                                Rp <?php echo e(number_format($event->registration_fee, 0, ',', '.')); ?>

                            <?php else: ?>
                                Gratis
                            <?php endif; ?>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Persetujuan Admin</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($event->requires_approval ? 'Diperlukan' : 'Tidak diperlukan'); ?>

                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Sertifikat</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($event->certificate_available ? 'Tersedia' : 'Tidak tersedia'); ?>

                        </dd>
                    </div>
                </div>
            </div>

            <!-- Contact Person -->
            <?php if($event->contact_person && is_array($event->contact_person) && count($event->contact_person) > 0): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Kontak Person</h3>
                <div class="space-y-3">
                    <?php if(isset($event->contact_person['name'])): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Nama</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($event->contact_person['name']); ?></dd>
                    </div>
                    <?php endif; ?>
                    <?php if(isset($event->contact_person['phone'])): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Telepon</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="tel:<?php echo e($event->contact_person['phone']); ?>" class="text-blue-600 hover:text-blue-800">
                                <?php echo e($event->contact_person['phone']); ?>

                            </a>
                        </dd>
                    </div>
                    <?php endif; ?>
                    <?php if(isset($event->contact_person['email'])): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="mailto:<?php echo e($event->contact_person['email']); ?>" class="text-blue-600 hover:text-blue-800">
                                <?php echo e($event->contact_person['email']); ?>

                            </a>
                        </dd>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistik</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Total Pendaftar</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($event->registrations->count()); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Approved</span>
                        <span class="text-sm font-medium text-green-600"><?php echo e($event->registrations->where('status', 'approved')->count()); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Pending</span>
                        <span class="text-sm font-medium text-yellow-600"><?php echo e($event->registrations->where('status', 'pending')->count()); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Rejected</span>
                        <span class="text-sm font-medium text-red-600"><?php echo e($event->registrations->where('status', 'rejected')->count()); ?></span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Aksi</h3>
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.events.edit', $event)); ?>" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                        <i class="fas fa-edit mr-2"></i>Edit Kegiatan
                    </a>
                    <?php if($event->status === 'draft'): ?>
                    <form action="<?php echo e(route('admin.events.publish', $event)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <button type="submit" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                                onclick="return confirm('Yakin ingin mempublikasikan event ini?')">
                            <i class="fas fa-check mr-2"></i>Publish Event
                        </button>
                    </form>
                    <?php endif; ?>
                    <?php if(in_array($event->status, ['draft', 'published'])): ?>
                    <form action="<?php echo e(route('admin.events.cancel', $event)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <button type="submit" 
                                class="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                                onclick="return confirm('Yakin ingin membatalkan event ini?')">
                            <i class="fas fa-ban mr-2"></i>Cancel Event
                        </button>
                    </form>
                    <?php endif; ?>
                    <form action="<?php echo e(route('admin.events.destroy', $event)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                                onclick="return confirm('Yakin ingin menghapus event <?php echo e($event->title); ?>? Aksi ini tidak dapat dibatalkan.')">
                            <i class="fas fa-trash mr-2"></i>Hapus Event
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\resources\views/admin/events/show.blade.php ENDPATH**/ ?>