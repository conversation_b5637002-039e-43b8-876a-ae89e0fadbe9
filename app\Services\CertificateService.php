<?php

namespace App\Services;

use App\Models\EventAttendance;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class CertificateService
{
    /**
     * Generate certificate for attendance
     */
    public function generateCertificate(EventAttendance $attendance)
    {
        $event = $attendance->event;
        $user = $attendance->user;

        // Check if event has certificate template
        if (!$event->certificate_template) {
            throw new \Exception('Event tidak memiliki template sertifikat.');
        }

        // Check if attendance is verified
        if ($attendance->verification_status !== 'verified' || $attendance->status !== 'present') {
            throw new \Exception('Absensi belum diverifikasi atau tidak hadir.');
        }

        // Generate certificate HTML
        $certificateHtml = $this->generateCertificateHtml($attendance);

        // Generate PDF
        $pdf = Pdf::loadHTML($certificateHtml)
                  ->setPaper('A4', 'landscape')
                  ->setOptions([
                      'isHtml5ParserEnabled' => true,
                      'isPhpEnabled' => true,
                      'defaultFont' => 'Arial',
                      'dpi' => 150,
                  ]);

        // Generate filename
        $filename = 'certificates/' . $event->slug . '_' . $user->student_id . '_' . time() . '.pdf';

        // Save PDF to storage
        Storage::disk('public')->put($filename, $pdf->output());

        // Update attendance record
        $attendance->update([
            'certificate_generated' => true,
            'certificate_file' => $filename,
        ]);

        return $filename;
    }

    /**
     * Generate certificate HTML with template overlay
     */
    private function generateCertificateHtml(EventAttendance $attendance)
    {
        $event = $attendance->event;
        $user = $attendance->user;
        $templateUrl = Storage::disk('public')->url($event->certificate_template);

        // Get template image dimensions (you might want to store this in database)
        $templatePath = Storage::disk('public')->path($event->certificate_template);
        
        // Basic certificate HTML with background image
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Sertifikat - ' . $event->title . '</title>
            <style>
                @page {
                    margin: 0;
                    size: A4 landscape;
                }
                body {
                    margin: 0;
                    padding: 0;
                    font-family: Arial, sans-serif;
                    background-image: url("' . $templateUrl . '");
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    width: 297mm;
                    height: 210mm;
                    position: relative;
                }
                .certificate-content {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                }
                .participant-name {
                    font-size: 36px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px 0;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .event-title {
                    font-size: 24px;
                    color: #34495e;
                    margin: 10px 0;
                    font-weight: 600;
                }
                .event-date {
                    font-size: 18px;
                    color: #7f8c8d;
                    margin: 10px 0;
                }
                .certificate-id {
                    position: absolute;
                    bottom: 30px;
                    right: 50px;
                    font-size: 12px;
                    color: #95a5a6;
                }
            </style>
        </head>
        <body>
            <div class="certificate-content">
                <div class="participant-name">
                    ' . strtoupper($user->name) . '
                </div>
                <div class="event-title">
                    ' . $event->title . '
                </div>
                <div class="event-date">
                    ' . $event->start_datetime->format('d F Y') . '
                </div>
            </div>
            <div class="certificate-id">
                Certificate ID: ' . $event->slug . '-' . $user->student_id . '-' . date('Ymd') . '
            </div>
        </body>
        </html>';

        return $html;
    }

    /**
     * Download certificate
     */
    public function downloadCertificate(EventAttendance $attendance)
    {
        if (!$attendance->canDownloadCertificate()) {
            throw new \Exception('Sertifikat tidak dapat didownload.');
        }

        // Generate certificate if not exists
        if (!$attendance->certificate_file) {
            $this->generateCertificate($attendance);
        }

        // Mark as downloaded
        $attendance->markCertificateDownloaded();

        $filename = 'Sertifikat_' . $attendance->event->title . '_' . $attendance->user->name . '.pdf';
        
        return Storage::disk('public')->download($attendance->certificate_file, $filename);
    }

    /**
     * Generate certificate with custom positioning (advanced version)
     */
    public function generateCertificateAdvanced(EventAttendance $attendance, $namePosition = null)
    {
        $event = $attendance->event;
        $user = $attendance->user;

        // Default name position (center of certificate)
        $defaultPosition = [
            'x' => 50, // percentage from left
            'y' => 50, // percentage from top
            'font_size' => 36,
            'color' => '#2c3e50'
        ];

        $position = $namePosition ?: $defaultPosition;

        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Sertifikat - ' . $event->title . '</title>
            <style>
                @page {
                    margin: 0;
                    size: A4 landscape;
                }
                body {
                    margin: 0;
                    padding: 0;
                    font-family: "Times New Roman", serif;
                    background-image: url("' . Storage::disk('public')->url($event->certificate_template) . '");
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    width: 297mm;
                    height: 210mm;
                    position: relative;
                }
                .participant-name {
                    position: absolute;
                    left: ' . $position['x'] . '%;
                    top: ' . $position['y'] . '%;
                    transform: translate(-50%, -50%);
                    font-size: ' . $position['font_size'] . 'px;
                    font-weight: bold;
                    color: ' . $position['color'] . ';
                    text-transform: uppercase;
                    letter-spacing: 3px;
                    text-align: center;
                    white-space: nowrap;
                }
            </style>
        </head>
        <body>
            <div class="participant-name">
                ' . strtoupper($user->name) . '
            </div>
        </body>
        </html>';

        // Generate PDF
        $pdf = Pdf::loadHTML($html)
                  ->setPaper('A4', 'landscape')
                  ->setOptions([
                      'isHtml5ParserEnabled' => true,
                      'isPhpEnabled' => true,
                      'defaultFont' => 'Times-Roman',
                      'dpi' => 300,
                  ]);

        // Generate filename
        $filename = 'certificates/' . $event->slug . '_' . $user->student_id . '_' . time() . '.pdf';

        // Save PDF to storage
        Storage::disk('public')->put($filename, $pdf->output());

        // Update attendance record
        $attendance->update([
            'certificate_generated' => true,
            'certificate_file' => $filename,
        ]);

        return $filename;
    }
}
