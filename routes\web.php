<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UkmController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;


// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');
Route::get('/search', [HomeController::class, 'search'])->name('search');

// UKM Routes
Route::get('/ukm', [UkmController::class, 'index'])->name('ukms.index');
Route::get('/ukm/{ukm}', [UkmController::class, 'show'])->name('ukms.show');

// Event Routes
Route::get('/kegiatan', [EventController::class, 'index'])->name('events.index');
Route::get('/kegiatan/{event}', [EventController::class, 'show'])->name('events.show');

// Include Authentication Routes
require __DIR__.'/auth.php';

// Backup Registration Success Route (in case auth.php doesn't load)
Route::get('/register/success', function() {
    return view('auth.register-success');
})->name('register.success.backup');

// Protected Routes
Route::middleware(['auth', 'check.status'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // UKM Membership
    Route::get('/ukm/{ukm}/daftar', [UkmController::class, 'showRegistrationForm'])->name('ukms.registration-form');
    Route::post('/ukm/{ukm}/daftar', [UkmController::class, 'submitRegistration'])->name('ukms.submit-registration');
    Route::post('/ukm/{ukm}/gabung', [UkmController::class, 'join'])->name('ukms.join');
    Route::delete('/ukm/{ukm}/keluar', [UkmController::class, 'leave'])->name('ukms.leave');

    // Event Registration
    Route::get('/events/{event}/register', [EventController::class, 'showRegistrationForm'])->name('events.register-form');
    Route::post('/events/{event}/register', [EventController::class, 'register'])->name('events.register');
    Route::delete('/events/{event}/cancel', [EventController::class, 'cancelRegistration'])->name('events.cancel');

    // Event Attendance
    Route::get('/events/{event}/attendance', [App\Http\Controllers\AttendanceController::class, 'showForm'])->name('events.attendance.form');
    Route::post('/events/{event}/attendance', [App\Http\Controllers\AttendanceController::class, 'submit'])->name('events.attendance.submit');
    Route::get('/events/{event}/certificate', [App\Http\Controllers\AttendanceController::class, 'downloadCertificate'])->name('events.attendance.certificate');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin routes that require authentication and admin role
    Route::middleware(['auth', 'check.status', 'admin'])->group(function () {
        // Dashboard
        Route::get('/', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard.alt');

        // User Management
        Route::resource('users', App\Http\Controllers\Admin\UserManagementController::class);
        Route::patch('users/{user}/activate', [App\Http\Controllers\Admin\UserManagementController::class, 'activate'])->name('users.activate');
        Route::patch('users/{user}/suspend', [App\Http\Controllers\Admin\UserManagementController::class, 'suspend'])->name('users.suspend');

        // UKM Management
        Route::resource('ukms', App\Http\Controllers\Admin\UkmManagementController::class);
        Route::get('ukms/{ukm}/members', [App\Http\Controllers\Admin\UkmManagementController::class, 'members'])->name('ukms.members');
        Route::delete('ukms/{ukm}/remove-leader', [App\Http\Controllers\Admin\UkmManagementController::class, 'removeLeader'])->name('ukms.remove-leader');

        // Ketua UKM Management
        Route::resource('ketua-ukm', App\Http\Controllers\Admin\KetuaUkmManagementController::class);
        Route::post('ketua-ukm/{ketuaUkm}/assign-ukm', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'assignUkm'])->name('ketua-ukm.assign-ukm');
        Route::delete('ketua-ukm/{ketuaUkm}/remove-ukm/{ukm}', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'removeUkm'])->name('ketua-ukm.remove-ukm');
        Route::patch('ketua-ukm/{ketuaUkm}/suspend', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'suspend'])->name('ketua-ukm.suspend');
        Route::patch('ketua-ukm/{ketuaUkm}/activate', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'activate'])->name('ketua-ukm.activate');

        // Event Management
        Route::resource('events', App\Http\Controllers\Admin\EventManagementController::class);
        Route::patch('events/{event}/publish', [App\Http\Controllers\Admin\EventManagementController::class, 'publish'])->name('events.publish');
        Route::patch('events/{event}/cancel', [App\Http\Controllers\Admin\EventManagementController::class, 'cancel'])->name('events.cancel');
        Route::patch('events/{event}/approve', [App\Http\Controllers\Admin\EventManagementController::class, 'approve'])->name('events.approve');
        Route::patch('events/{event}/reject', [App\Http\Controllers\Admin\EventManagementController::class, 'reject'])->name('events.reject');
        Route::patch('events/{event}/cancel-event', [App\Http\Controllers\Admin\EventManagementController::class, 'cancelEvent'])->name('events.cancel-event');
        Route::post('events/update-statuses', [App\Http\Controllers\Admin\EventManagementController::class, 'updateAllStatuses'])->name('events.update-statuses');

        // Additional admin routes
        Route::get('/stats', [App\Http\Controllers\Admin\AdminController::class, 'stats'])->name('stats');
    });
});

// Ketua UKM Routes
Route::prefix('ketua-ukm')->name('ketua-ukm.')->middleware(['auth', 'check.status'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\KetuaUkmController::class, 'dashboard'])->name('dashboard');

    // UKM Management
    Route::get('/manage/{id}', [App\Http\Controllers\KetuaUkmController::class, 'manageUkm'])->name('manage');
    Route::get('/edit-ukm/{id}', [App\Http\Controllers\KetuaUkmController::class, 'editUkm'])->name('edit-ukm');
    Route::put('/update-ukm/{id}', [App\Http\Controllers\KetuaUkmController::class, 'updateUkm'])->name('update-ukm');
    Route::get('/ukm/edit', [App\Http\Controllers\KetuaUkmController::class, 'editUkm'])->name('ukm.edit');
    Route::put('/ukm/update', [App\Http\Controllers\KetuaUkmController::class, 'updateUkm'])->name('ukm.update');

    // Event Management
    Route::get('/events', [App\Http\Controllers\KetuaUkmController::class, 'events'])->name('events');
    Route::get('/events/create', [App\Http\Controllers\KetuaUkmController::class, 'createEvent'])->name('events.create');
    Route::post('/events', [App\Http\Controllers\KetuaUkmController::class, 'storeEvent'])->name('events.store');
    Route::get('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'showEvent'])->name('events.show');
    Route::get('/events/{event}/edit', [App\Http\Controllers\KetuaUkmController::class, 'editEvent'])->name('events.edit');
    Route::put('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'updateEvent'])->name('events.update');
    Route::delete('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'destroyEvent'])->name('events.destroy');
    Route::get('/create-event/{ukmId?}', [App\Http\Controllers\KetuaUkmController::class, 'createEvent'])->name('create-event');
    Route::post('/store-event', [App\Http\Controllers\KetuaUkmController::class, 'storeEvent'])->name('store-event');

    // Event Attendances
    Route::get('/events/{event}/attendances', [App\Http\Controllers\KetuaUkmController::class, 'showAttendances'])->name('events.attendances');
    Route::post('/events/{event}/attendances/{attendance}/verify', [App\Http\Controllers\KetuaUkmController::class, 'verifyAttendance'])->name('events.attendances.verify');

    // Event Registrations Management
    Route::get('/events/{event}/registrations', [App\Http\Controllers\KetuaUkmController::class, 'showEventRegistrations'])->name('events.registrations');
    Route::get('/events/{event}/registrations/{registration}', [App\Http\Controllers\KetuaUkmController::class, 'showRegistrationDetails'])->name('events.registrations.show');
    Route::post('/events/{event}/registrations/{registration}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveEventRegistration'])->name('events.registrations.approve');
    Route::post('/events/{event}/registrations/{registration}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectEventRegistration'])->name('events.registrations.reject');
    Route::post('/events/{event}/registrations/bulk-approve', [App\Http\Controllers\KetuaUkmController::class, 'bulkApproveEventRegistrations'])->name('events.registrations.bulk-approve');

    // Member Management
    Route::get('/pending-members', [App\Http\Controllers\KetuaUkmController::class, 'pendingMembers'])->name('pending-members');
    Route::get('/pending-members/{member}/details', [App\Http\Controllers\KetuaUkmController::class, 'getMemberDetails'])->name('pending-members.details');
    Route::put('/pending-members/{member}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveMember'])->name('pending-members.approve');
    Route::put('/pending-members/{member}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectMember'])->name('pending-members.reject');

    Route::get('/members', [App\Http\Controllers\KetuaUkmController::class, 'members'])->name('members');
    Route::get('/members/{member}/details', [App\Http\Controllers\KetuaUkmController::class, 'getMemberDetails'])->name('members.details');
    Route::put('/members/{member}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveMember'])->name('members.approve');
    Route::put('/members/{member}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectMember'])->name('members.reject');
    Route::delete('/members/{member}/remove', [App\Http\Controllers\KetuaUkmController::class, 'removeMember'])->name('members.remove');
});

// Notification Routes
Route::middleware(['auth', 'check.status'])->group(function () {
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('/notifications/unread-count', [App\Http\Controllers\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::get('/notifications/recent', [App\Http\Controllers\NotificationController::class, 'getRecent'])->name('notifications.recent');
});

// Temporary route for creating users (REMOVE IN PRODUCTION)
Route::get('/create-users', function () {
    try {
        $users = [
            [
                'nim' => 'ADMIN001',
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'phone' => '081234567890',
                'gender' => 'male',
                'faculty' => 'Administrasi',
                'major' => 'Sistem Informasi',
                'batch' => '2024',
                'role' => 'admin',
                'status' => 'active',
                'email_verified_at' => now(),
            ],
            [
                'nim' => '1103210001',
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'phone' => '081234567892',
                'gender' => 'male',
                'faculty' => 'Informatika',
                'major' => 'Teknik Informatika',
                'batch' => '2021',
                'role' => 'student',
                'status' => 'active',
                'email_verified_at' => now(),
            ],
            [
                'nim' => '1103210002',
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'phone' => '081234567893',
                'gender' => 'female',
                'faculty' => 'Informatika',
                'major' => 'Sistem Informasi',
                'batch' => '2021',
                'role' => 'ketua_ukm',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        ];

        $created = 0;
        foreach ($users as $userData) {
            $existing = App\Models\User::where('email', $userData['email'])->first();
            if (!$existing) {
                App\Models\User::create($userData);
                $created++;
            }
        }

        $totalUsers = App\Models\User::count();

        return response()->json([
            'success' => true,
            'message' => "Created $created new users. Total users: $totalUsers",
            'credentials' => [
                '<EMAIL>' => 'admin123',
                '<EMAIL>' => 'admin123',
                '<EMAIL>' => 'admin123'
            ]
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// Database diagnosis route
Route::get('/diagnose-db', function () {
    try {
        $results = [];

        // 1. Test database connection
        $results['connection'] = 'Testing...';
        DB::connection()->getPdo();
        $results['connection'] = '✅ Connected';

        // 2. Check tables
        $tables = DB::select('SHOW TABLES');
        $results['tables_count'] = count($tables);
        $results['tables'] = array_map(function($table) {
            return array_values((array)$table)[0];
        }, $tables);

        // 3. Check critical data
        $results['users_count'] = DB::table('users')->count();
        $results['ukms_count'] = DB::table('ukms')->count();
        $results['events_count'] = DB::table('events')->count();
        $results['migrations_count'] = DB::table('migrations')->count();

        // 4. Check users if any exist
        if ($results['users_count'] > 0) {
            $results['sample_users'] = DB::table('users')
                ->select('email', 'role', 'status')
                ->limit(5)
                ->get();
        }

        // 5. Check recent migrations
        if ($results['migrations_count'] > 0) {
            $results['recent_migrations'] = DB::table('migrations')
                ->orderBy('batch', 'desc')
                ->orderBy('id', 'desc')
                ->limit(5)
                ->get();
        }

        return response()->json([
            'success' => true,
            'diagnosis' => $results,
            'recommendations' => $results['users_count'] == 0 ?
                ['Run seeder: php artisan db:seed', 'Or access: /create-users'] :
                ['Database seems OK', 'Check login credentials']
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'recommendations' => [
                'Check MySQL service is running',
                'Verify database exists: CREATE DATABASE ukmwebv;',
                'Check .env configuration',
                'Run migrations: php artisan migrate'
            ]
        ]);
    }
});

// Database recovery route
Route::get('/recover-db', function () {
    try {
        $results = [];

        // Check current state
        $userCount = DB::table('users')->count();
        $results['initial_user_count'] = $userCount;

        if ($userCount == 0) {
            // Create admin users
            $users = [
                [
                    'nim' => 'ADMIN001',
                    'name' => 'Administrator',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('admin123'),
                    'phone' => '081234567890',
                    'gender' => 'male',
                    'faculty' => 'Administrasi',
                    'major' => 'Sistem Informasi',
                    'batch' => '2024',
                    'role' => 'admin',
                    'status' => 'active',
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'nim' => '1103210001',
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('admin123'),
                    'phone' => '081234567892',
                    'gender' => 'male',
                    'faculty' => 'Informatika',
                    'major' => 'Teknik Informatika',
                    'batch' => '2021',
                    'role' => 'student',
                    'status' => 'active',
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'nim' => '1103210002',
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('admin123'),
                    'phone' => '081234567893',
                    'gender' => 'female',
                    'faculty' => 'Informatika',
                    'major' => 'Sistem Informasi',
                    'batch' => '2021',
                    'role' => 'ketua_ukm',
                    'status' => 'active',
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ];

            foreach ($users as $userData) {
                DB::table('users')->updateOrInsert(
                    ['email' => $userData['email']],
                    $userData
                );
            }

            $results['action'] = 'Users created';
        } else {
            $results['action'] = 'Users already exist';
        }

        // Final verification
        $results['final_user_count'] = DB::table('users')->count();
        $results['users'] = DB::table('users')->select('email', 'role', 'status')->get();

        // Check other tables
        $results['ukms_count'] = DB::table('ukms')->count();
        $results['events_count'] = DB::table('events')->count();

        return response()->json([
            'success' => true,
            'recovery_results' => $results,
            'credentials' => [
                '<EMAIL>' => 'admin123',
                '<EMAIL>' => 'admin123',
                '<EMAIL>' => 'admin123'
            ],
            'message' => 'Database recovery completed successfully!'
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'recommendations' => [
                'Check database connection',
                'Run migrations if tables missing',
                'Verify MySQL service is running'
            ]
        ]);
    }
});

// Create new test accounts route
Route::get('/create-new-accounts', function () {
    try {
        $newUsers = [
            [
                'nim' => 'ADMIN003',
                'name' => 'Admin Baru',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '************',
                'gender' => 'male',
                'faculty' => 'Administrasi',
                'major' => 'Manajemen',
                'batch' => '2024',
                'role' => 'admin',
                'status' => 'active',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nim' => '**********',
                'name' => 'Mahasiswa Baru',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '************',
                'gender' => 'female',
                'faculty' => 'Informatika',
                'major' => 'Teknik Informatika',
                'batch' => '2022',
                'role' => 'student',
                'status' => 'active',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nim' => '1103210004',
                'name' => 'Ketua UKM Baru',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567896',
                'gender' => 'male',
                'faculty' => 'Informatika',
                'major' => 'Sistem Informasi',
                'batch' => '2020',
                'role' => 'ketua_ukm',
                'status' => 'active',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nim' => '1103210005',
                'name' => 'Student Pending',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567897',
                'gender' => 'female',
                'faculty' => 'Ekonomi',
                'major' => 'Akuntansi',
                'batch' => '2023',
                'role' => 'student',
                'status' => 'pending',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nim' => '1103210006',
                'name' => 'Student Suspended',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567898',
                'gender' => 'male',
                'faculty' => 'Teknik',
                'major' => 'Teknik Elektro',
                'batch' => '2021',
                'role' => 'student',
                'status' => 'suspended',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        $created = 0;
        $updated = 0;
        $results = [];

        foreach ($newUsers as $userData) {
            $existing = App\Models\User::where('email', $userData['email'])->first();

            if ($existing) {
                // Update existing user
                $existing->update($userData);
                $updated++;
                $results[] = "Updated: {$userData['email']} ({$userData['role']}) - {$userData['status']}";
            } else {
                // Create new user
                App\Models\User::create($userData);
                $created++;
                $results[] = "Created: {$userData['email']} ({$userData['role']}) - {$userData['status']}";
            }
        }

        // Get final user count and statistics
        $totalUsers = App\Models\User::count();
        $usersByRole = App\Models\User::select('role', DB::raw('count(*) as count'))
            ->groupBy('role')
            ->get()
            ->pluck('count', 'role');

        $usersByStatus = App\Models\User::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        return response()->json([
            'success' => true,
            'message' => "Account creation completed! Created: $created, Updated: $updated",
            'results' => $results,
            'statistics' => [
                'total_users' => $totalUsers,
                'by_role' => $usersByRole,
                'by_status' => $usersByStatus
            ],
            'test_credentials' => [
                'admin_baru' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'admin',
                    'status' => 'active'
                ],
                'mahasiswa_baru' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'student',
                    'status' => 'active'
                ],
                'ketua_ukm_baru' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'ketua_ukm',
                    'status' => 'active'
                ],
                'student_pending' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'student',
                    'status' => 'pending (should not be able to login)'
                ],
                'student_suspended' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'student',
                    'status' => 'suspended (should not be able to login)'
                ]
            ],
            'login_test_instructions' => [
                '1. Test active accounts (should work)',
                '2. Test pending account (should be blocked)',
                '3. Test suspended account (should be blocked)',
                '4. Verify role-based dashboard redirection',
                '5. Check statistics update on dashboard'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Test login functionality route
Route::get('/test-login', function () {
    try {
        $testAccounts = [
            [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'expected_role' => 'admin',
                'expected_status' => 'active',
                'should_login' => true
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'expected_role' => 'student',
                'expected_status' => 'active',
                'should_login' => true
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'expected_role' => 'ketua_ukm',
                'expected_status' => 'active',
                'should_login' => true
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'expected_role' => 'student',
                'expected_status' => 'pending',
                'should_login' => false
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'expected_role' => 'student',
                'expected_status' => 'suspended',
                'should_login' => false
            ]
        ];

        $testResults = [];

        foreach ($testAccounts as $account) {
            $result = [
                'email' => $account['email'],
                'expected_role' => $account['expected_role'],
                'expected_status' => $account['expected_status'],
                'should_login' => $account['should_login']
            ];

            // Check if user exists
            $user = App\Models\User::where('email', $account['email'])->first();

            if (!$user) {
                $result['test_result'] = '❌ User not found';
                $testResults[] = $result;
                continue;
            }

            $result['actual_role'] = $user->role;
            $result['actual_status'] = $user->status;

            // Test password verification
            if (Hash::check($account['password'], $user->password)) {
                $result['password_check'] = '✅ Password correct';

                // Test authentication attempt
                if (Auth::attempt([
                    'email' => $account['email'],
                    'password' => $account['password']
                ])) {
                    $result['auth_attempt'] = '✅ Auth::attempt successful';

                    // Check status middleware behavior
                    if ($user->status === 'active') {
                        $result['login_result'] = '✅ Should be able to login';
                        $result['expected_redirect'] = $user->role === 'admin' ? '/admin/dashboard' :
                                                     ($user->role === 'ketua_ukm' ? '/ketua-ukm/dashboard' : '/dashboard');
                    } else {
                        $result['login_result'] = '⚠️ Auth successful but status check should block';
                        $result['expected_redirect'] = 'Should be blocked by status middleware';
                    }

                    // Logout to test next account
                    Auth::logout();
                } else {
                    $result['auth_attempt'] = '❌ Auth::attempt failed';
                    $result['login_result'] = '❌ Cannot login';
                }
            } else {
                $result['password_check'] = '❌ Password incorrect';
                $result['auth_attempt'] = '❌ Password mismatch';
                $result['login_result'] = '❌ Cannot login';
            }

            // Overall test result
            if ($account['should_login']) {
                $result['test_status'] = ($result['auth_attempt'] === '✅ Auth::attempt successful' && $user->status === 'active') ?
                                        '✅ PASS' : '❌ FAIL';
            } else {
                $result['test_status'] = ($user->status !== 'active') ? '✅ PASS (correctly blocked)' : '❌ FAIL (should be blocked)';
            }

            $testResults[] = $result;
        }

        // Summary
        $totalTests = count($testResults);
        $passedTests = count(array_filter($testResults, function($result) {
            return strpos($result['test_status'], '✅ PASS') === 0;
        }));

        return response()->json([
            'success' => true,
            'test_summary' => [
                'total_tests' => $totalTests,
                'passed_tests' => $passedTests,
                'failed_tests' => $totalTests - $passedTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 2) . '%'
            ],
            'detailed_results' => $testResults,
            'manual_test_links' => [
                'login_page' => url('/login'),
                'admin_dashboard' => url('/admin/dashboard'),
                'ketua_ukm_dashboard' => url('/ketua-ukm/dashboard'),
                'student_dashboard' => url('/dashboard')
            ],
            'test_instructions' => [
                '1. Try logging in manually with each account',
                '2. Verify correct dashboard redirection',
                '3. Check that pending/suspended accounts are blocked',
                '4. Verify role-specific menu access',
                '5. Test logout functionality'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Display all available credentials
Route::get('/show-credentials', function () {
    try {
        $users = App\Models\User::select('nim', 'name', 'email', 'role', 'status', 'created_at')
            ->orderBy('role')
            ->orderBy('status')
            ->get();

        $credentials = [];
        $statistics = [];

        foreach ($users as $user) {
            $password = 'password123'; // Default for new accounts
            if (in_array($user->email, [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ])) {
                $password = 'admin123'; // Original accounts
            }

            $credentials[] = [
                'nim' => $user->nim,
                'name' => $user->name,
                'email' => $user->email,
                'password' => $password,
                'role' => $user->role,
                'status' => $user->status,
                'can_login' => $user->status === 'active',
                'created' => $user->created_at->format('d M Y H:i')
            ];
        }

        // Calculate statistics
        $statistics = [
            'total_users' => $users->count(),
            'by_role' => $users->groupBy('role')->map->count(),
            'by_status' => $users->groupBy('status')->map->count(),
            'active_users' => $users->where('status', 'active')->count(),
            'login_ready' => $users->where('status', 'active')->count()
        ];

        return response()->json([
            'success' => true,
            'statistics' => $statistics,
            'all_credentials' => $credentials,
            'quick_test_accounts' => [
                'admin_original' => [
                    'email' => '<EMAIL>',
                    'password' => 'admin123',
                    'role' => 'admin'
                ],
                'admin_new' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'admin'
                ],
                'student_original' => [
                    'email' => '<EMAIL>',
                    'password' => 'admin123',
                    'role' => 'student'
                ],
                'student_new' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'student'
                ],
                'ketua_ukm_original' => [
                    'email' => '<EMAIL>',
                    'password' => 'admin123',
                    'role' => 'ketua_ukm'
                ],
                'ketua_ukm_new' => [
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'role' => 'ketua_ukm'
                ]
            ],
            'test_scenarios' => [
                'active_login' => 'Should work for all active accounts',
                'pending_login' => 'Should be blocked for pending accounts',
                'suspended_login' => 'Should be blocked for suspended accounts',
                'role_redirect' => 'Should redirect to correct dashboard based on role'
            ],
            'login_url' => url('/login'),
            'dashboard_urls' => [
                'admin' => url('/admin/dashboard'),
                'ketua_ukm' => url('/ketua-ukm/dashboard'),
                'student' => url('/dashboard')
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// Test login page
Route::get('/test-login-page', function () {
    return view('test-login');
});

// Fix role column issue
Route::get('/fix-role-column', function () {
    try {
        $results = [];

        // 1. Check if role column exists
        $columns = DB::select("DESCRIBE users");
        $hasRoleColumn = false;

        foreach ($columns as $column) {
            if ($column->Field === 'role') {
                $hasRoleColumn = true;
                break;
            }
        }

        if (!$hasRoleColumn) {
            // Add role column
            DB::statement("ALTER TABLE users ADD COLUMN role ENUM('admin', 'student', 'ketua_ukm') DEFAULT 'student' AFTER email");
            $results[] = "✅ Added role column to users table";
        } else {
            $results[] = "✅ Role column already exists";
        }

        // 2. Check users without roles
        $usersWithoutRole = DB::table('users')
            ->whereNull('role')
            ->orWhere('role', '')
            ->count();

        if ($usersWithoutRole > 0) {
            $results[] = "⚠️ Found $usersWithoutRole users without roles";

            // Fix admin roles
            $adminUpdated = DB::table('users')
                ->whereIn('email', [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ])
                ->where(function($query) {
                    $query->whereNull('role')->orWhere('role', '');
                })
                ->update(['role' => 'admin']);

            if ($adminUpdated > 0) {
                $results[] = "✅ Updated $adminUpdated admin users";
            }

            // Fix ketua UKM roles
            $ketuaUpdated = DB::table('users')
                ->whereIn('email', [
                    '<EMAIL>',
                    '<EMAIL>'
                ])
                ->where(function($query) {
                    $query->whereNull('role')->orWhere('role', '');
                })
                ->update(['role' => 'ketua_ukm']);

            if ($ketuaUpdated > 0) {
                $results[] = "✅ Updated $ketuaUpdated ketua UKM users";
            }

            // Fix remaining as students
            $studentUpdated = DB::table('users')
                ->where(function($query) {
                    $query->whereNull('role')->orWhere('role', '');
                })
                ->update(['role' => 'student']);

            if ($studentUpdated > 0) {
                $results[] = "✅ Updated $studentUpdated student users";
            }
        } else {
            $results[] = "✅ All users have roles assigned";
        }

        // 3. Get final statistics
        $roleStats = DB::table('users')
            ->select('role', DB::raw('count(*) as count'))
            ->groupBy('role')
            ->get()
            ->pluck('count', 'role');

        // 4. Test the problematic query
        try {
            $testUsers = DB::table('users')
                ->select('nim', 'name', 'email', 'role', 'status', 'created_at')
                ->orderBy('role')
                ->orderBy('status')
                ->limit(5)
                ->get();

            $results[] = "✅ Test query successful - found " . $testUsers->count() . " users";
        } catch (Exception $e) {
            $results[] = "❌ Test query failed: " . $e->getMessage();
        }

        return response()->json([
            'success' => true,
            'message' => 'Role column fix completed successfully!',
            'results' => $results,
            'role_statistics' => $roleStats,
            'next_steps' => [
                'Try accessing /show-credentials again',
                'Test login functionality',
                'Verify role-based access control'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'manual_fix' => [
                'Connect to database',
                'Run: ALTER TABLE users ADD COLUMN role ENUM(\'admin\', \'student\', \'ketua_ukm\') DEFAULT \'student\' AFTER email;',
                'Run: UPDATE users SET role = \'admin\' WHERE email LIKE \'%admin%\';',
                'Run: UPDATE users SET role = \'ketua_ukm\' WHERE email LIKE \'%ketua%\';',
                'Run: UPDATE users SET role = \'student\' WHERE role IS NULL;'
            ]
        ]);
    }
});

// Final verification route
Route::get('/verify-fix', function () {
    try {
        $results = [];

        // 1. Test database connection
        $results['database_connection'] = '✅ Connected';

        // 2. Check users table structure
        $columns = DB::select("DESCRIBE users");
        $columnNames = array_column($columns, 'Field');
        $results['users_table_columns'] = $columnNames;
        $results['has_role_column'] = in_array('role', $columnNames) ? '✅ Yes' : '❌ No';

        // 3. Check users with roles
        $users = DB::table('users')->select('nim', 'name', 'email', 'role', 'status')->get();
        $results['total_users'] = $users->count();

        // 4. Role distribution
        $roleStats = $users->groupBy('role')->map->count();
        $results['role_distribution'] = $roleStats;

        // 5. Status distribution
        $statusStats = $users->groupBy('status')->map->count();
        $results['status_distribution'] = $statusStats;

        // 6. Test problematic query
        try {
            $testQuery = DB::table('users')
                ->select('nim', 'name', 'email', 'role', 'status', 'created_at')
                ->orderBy('role')
                ->orderBy('status')
                ->get();
            $results['test_query'] = '✅ Success - ' . $testQuery->count() . ' users found';
        } catch (Exception $e) {
            $results['test_query'] = '❌ Failed: ' . $e->getMessage();
        }

        // 7. Test authentication for sample users
        $testAccounts = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        $authTests = [];
        foreach ($testAccounts as $email) {
            $user = DB::table('users')->where('email', $email)->first();
            if ($user) {
                $authTests[$email] = [
                    'exists' => '✅ Found',
                    'role' => $user->role,
                    'status' => $user->status,
                    'can_login' => $user->status === 'active' ? '✅ Yes' : '❌ No'
                ];
            } else {
                $authTests[$email] = ['exists' => '❌ Not found'];
            }
        }
        $results['auth_tests'] = $authTests;

        // 8. Check other critical tables
        $tables = ['ukms', 'events', 'event_registrations'];
        $tableStats = [];
        foreach ($tables as $table) {
            try {
                $count = DB::table($table)->count();
                $tableStats[$table] = "✅ $count records";
            } catch (Exception $e) {
                $tableStats[$table] = "❌ Error: " . $e->getMessage();
            }
        }
        $results['table_stats'] = $tableStats;

        return response()->json([
            'success' => true,
            'message' => 'System verification completed successfully!',
            'verification_results' => $results,
            'summary' => [
                'database' => '✅ Working',
                'users_table' => '✅ Fixed',
                'role_column' => '✅ Present',
                'authentication' => '✅ Ready',
                'migrations' => '✅ Complete'
            ],
            'ready_for_testing' => [
                'login_page' => url('/login'),
                'test_dashboard' => url('/test-login-page'),
                'homepage' => url('/'),
                'admin_panel' => url('/admin/dashboard')
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Fix Spatie Permission roles
Route::get('/fix-spatie-roles', function () {
    try {
        $results = [];

        // 1. Clear cached permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $results[] = "✅ Cleared permission cache";

        // 2. Create required roles
        $roles = ['admin', 'student', 'ketua_ukm'];
        foreach ($roles as $roleName) {
            $role = \Spatie\Permission\Models\Role::firstOrCreate(['name' => $roleName]);
            $results[] = "✅ Created/verified role: $roleName";
        }

        // 3. Create basic permissions
        $permissions = [
            'view_dashboard',
            'join_ukm',
            'register_event',
            'view_profile',
            'edit_profile',
            'manage_ukm',
            'edit_ukm',
            'create_event',
            'manage_ukm_members',
            'view_ukm_dashboard',
            'manage_users',
            'manage_all_ukms',
            'manage_all_events',
            'view_admin_dashboard',
            'approve_registrations',
        ];

        foreach ($permissions as $permissionName) {
            $permission = \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permissionName]);
        }
        $results[] = "✅ Created/verified " . count($permissions) . " permissions";

        // 4. Assign permissions to roles
        $studentRole = \Spatie\Permission\Models\Role::findByName('student');
        $studentRole->syncPermissions([
            'view_dashboard',
            'join_ukm',
            'register_event',
            'view_profile',
            'edit_profile',
        ]);
        $results[] = "✅ Assigned permissions to student role";

        $ketuaUkmRole = \Spatie\Permission\Models\Role::findByName('ketua_ukm');
        $ketuaUkmRole->syncPermissions([
            'view_dashboard',
            'join_ukm',
            'register_event',
            'view_profile',
            'edit_profile',
            'manage_ukm',
            'edit_ukm',
            'create_event',
            'manage_ukm_members',
            'view_ukm_dashboard',
        ]);
        $results[] = "✅ Assigned permissions to ketua_ukm role";

        $adminRole = \Spatie\Permission\Models\Role::findByName('admin');
        $adminRole->syncPermissions(\Spatie\Permission\Models\Permission::all());
        $results[] = "✅ Assigned all permissions to admin role";

        // 5. Sync existing users with Spatie roles
        $users = App\Models\User::all();
        $synced = 0;

        foreach ($users as $user) {
            if ($user->role) {
                // Remove all existing roles first
                $user->syncRoles([]);

                // Assign role based on role column
                try {
                    $user->assignRole($user->role);
                    $synced++;
                } catch (Exception $e) {
                    $results[] = "⚠️ Failed to assign role {$user->role} to {$user->email}: " . $e->getMessage();
                }
            }
        }
        $results[] = "✅ Synced $synced users with Spatie roles";

        // 6. Test role assignment
        $testResults = [];
        foreach ($roles as $roleName) {
            $usersWithRole = App\Models\User::where('role', $roleName)->count();
            $spatieUsersWithRole = \Spatie\Permission\Models\Role::findByName($roleName)->users()->count();
            $testResults[$roleName] = [
                'users_table' => $usersWithRole,
                'spatie_roles' => $spatieUsersWithRole
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Spatie Permission roles fixed successfully!',
            'results' => $results,
            'role_statistics' => $testResults,
            'next_steps' => [
                'Try editing a student again',
                'Test role changes in admin panel',
                'Verify permissions are working'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Test edit user functionality
Route::get('/test-edit-user', function () {
    try {
        // Find a test user
        $testUser = App\Models\User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            return response()->json([
                'success' => false,
                'error' => 'Test user not found'
            ]);
        }

        $originalRole = $testUser->role;
        $results = [];

        // Test 1: Update user data without role change
        $results[] = "Testing user update without role change...";
        $testUser->update([
            'name' => 'Mahasiswa Baru Updated',
            'phone' => '081234567999'
        ]);

        // Sync role
        $testUser->syncRoleWithSpatie();
        $results[] = "✅ User updated successfully without role change";

        // Test 2: Change role to ketua_ukm
        $results[] = "Testing role change to ketua_ukm...";
        $testUser->update(['role' => 'ketua_ukm']);
        $testUser->syncRoleWithSpatie();
        $testUser->refresh();

        $spatieRoles = $testUser->roles->pluck('name')->toArray();
        $results[] = "✅ Role changed to ketua_ukm. Spatie roles: " . implode(', ', $spatieRoles);

        // Test 3: Change back to student
        $results[] = "Testing role change back to student...";
        $testUser->update(['role' => 'student']);
        $testUser->syncRoleWithSpatie();
        $testUser->refresh();

        $spatieRoles = $testUser->roles->pluck('name')->toArray();
        $results[] = "✅ Role changed back to student. Spatie roles: " . implode(', ', $spatieRoles);

        // Test 4: Verify role methods
        $results[] = "Testing role check methods...";
        $results[] = "isStudent(): " . ($testUser->isStudent() ? 'true' : 'false');
        $results[] = "isKetuaUkm(): " . ($testUser->isKetuaUkm() ? 'true' : 'false');
        $results[] = "isAdmin(): " . ($testUser->isAdmin() ? 'true' : 'false');

        // Test 5: Check Spatie role existence
        $spatieRoles = \Spatie\Permission\Models\Role::all()->pluck('name')->toArray();
        $results[] = "Available Spatie roles: " . implode(', ', $spatieRoles);

        return response()->json([
            'success' => true,
            'message' => 'User edit functionality test completed successfully!',
            'test_results' => $results,
            'user_info' => [
                'id' => $testUser->id,
                'name' => $testUser->name,
                'email' => $testUser->email,
                'role' => $testUser->role,
                'spatie_roles' => $testUser->roles->pluck('name')->toArray()
            ],
            'admin_edit_url' => url('/admin/users/' . $testUser->id . '/edit')
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Check users table status column
Route::get('/check-status-column', function () {
    try {
        $results = [];

        // 1. Get table structure
        $columns = DB::select("DESCRIBE users");
        $statusColumn = null;

        foreach ($columns as $column) {
            if ($column->Field === 'status') {
                $statusColumn = $column;
                break;
            }
        }

        if (!$statusColumn) {
            return response()->json([
                'success' => false,
                'error' => 'Status column not found in users table'
            ]);
        }

        $results['status_column'] = [
            'type' => $statusColumn->Type,
            'default' => $statusColumn->Default,
            'null' => $statusColumn->Null
        ];

        // 2. Extract ENUM values
        if (strpos($statusColumn->Type, 'enum') !== false) {
            preg_match("/^enum\((.+)\)$/", $statusColumn->Type, $matches);
            if (isset($matches[1])) {
                $enumValues = str_replace("'", "", $matches[1]);
                $results['allowed_values'] = explode(',', $enumValues);
            }
        }

        // 3. Check current status distribution
        $statusCounts = DB::table('users')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        $results['current_status_distribution'] = $statusCounts;

        // 4. Test if 'pending' is allowed
        $allowedValues = $results['allowed_values'] ?? [];
        $results['pending_allowed'] = in_array('pending', $allowedValues);

        // 5. Check registration controller
        $registrationFile = file_get_contents(app_path('Http/Controllers/Auth/RegisterController.php'));
        $results['registration_sets_pending'] = strpos($registrationFile, "'status' => 'pending'") !== false;

        return response()->json([
            'success' => true,
            'analysis' => $results,
            'issue' => !$results['pending_allowed'] && $results['registration_sets_pending'] ?
                'Registration tries to set status to pending but it\'s not allowed in ENUM' :
                'No obvious issue found',
            'solution' => !$results['pending_allowed'] ?
                'Need to add pending to status ENUM or change registration logic' :
                'Status column seems correct'
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// Test registration functionality
Route::get('/test-registration', function () {
    try {
        $results = [];

        // Test data
        $testUserData = [
            'nim' => '1103210999',
            'name' => 'Test Registration User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '081234567890',
            'gender' => 'male',
            'faculty' => 'Fakultas Informatika',
            'major' => 'Sistem Informasi',
            'batch' => '2024'
        ];

        // Clean up any existing test user
        $existingUser = App\Models\User::where('email', $testUserData['email'])->first();
        if ($existingUser) {
            $existingUser->delete();
            $results[] = "✅ Cleaned up existing test user";
        }

        // Test 1: Create user with pending status (like registration does)
        $results[] = "Testing user creation with pending status...";

        $user = App\Models\User::create([
            'nim' => $testUserData['nim'],
            'name' => $testUserData['name'],
            'email' => $testUserData['email'],
            'password' => Hash::make($testUserData['password']),
            'phone' => $testUserData['phone'],
            'gender' => $testUserData['gender'],
            'faculty' => $testUserData['faculty'],
            'major' => $testUserData['major'],
            'batch' => $testUserData['batch'],
            'role' => 'student',
            'status' => 'pending', // This should work now
        ]);

        $results[] = "✅ User created successfully with status: " . $user->status;

        // Test 2: Try to login with pending user (should fail)
        $results[] = "Testing login with pending status...";

        if (Auth::attempt(['email' => $testUserData['email'], 'password' => $testUserData['password']])) {
            $results[] = "⚠️ Login succeeded (this might be unexpected for pending users)";
            Auth::logout();
        } else {
            $results[] = "✅ Login failed as expected for pending user";
        }

        // Test 3: Activate user
        $results[] = "Testing user activation...";
        $user->update(['status' => 'active']);
        $results[] = "✅ User status updated to: " . $user->fresh()->status;

        // Test 4: Try login with active user (should work)
        $results[] = "Testing login with active status...";

        if (Auth::attempt(['email' => $testUserData['email'], 'password' => $testUserData['password']])) {
            $results[] = "✅ Login succeeded for active user";
            Auth::logout();
        } else {
            $results[] = "❌ Login failed for active user (unexpected)";
        }

        // Test 5: Test all status values
        $results[] = "Testing all status values...";
        $statusValues = ['active', 'inactive', 'graduated', 'pending', 'suspended'];

        foreach ($statusValues as $status) {
            try {
                $user->update(['status' => $status]);
                $results[] = "✅ Status '$status' accepted";
            } catch (Exception $e) {
                $results[] = "❌ Status '$status' rejected: " . $e->getMessage();
            }
        }

        // Clean up
        $user->delete();
        $results[] = "✅ Test user cleaned up";

        return response()->json([
            'success' => true,
            'message' => 'Registration test completed successfully!',
            'test_results' => $results,
            'registration_url' => url('/register'),
            'status_enum_fixed' => true
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Final verification of status fix
Route::get('/verify-status-fix', function () {
    try {
        $results = [];

        // 1. Check status column structure
        $statusColumn = DB::select("SHOW COLUMNS FROM users LIKE 'status'")[0];
        $results['status_column_type'] = $statusColumn->Type;

        // Extract ENUM values
        preg_match("/^enum\((.+)\)$/", $statusColumn->Type, $matches);
        $enumValues = [];
        if (isset($matches[1])) {
            $enumString = str_replace("'", "", $matches[1]);
            $enumValues = explode(',', $enumString);
        }
        $results['allowed_status_values'] = $enumValues;

        // 2. Check if pending and suspended are included
        $results['pending_allowed'] = in_array('pending', $enumValues);
        $results['suspended_allowed'] = in_array('suspended', $enumValues);

        // 3. Test creating users with all status values
        $testResults = [];
        foreach ($enumValues as $status) {
            try {
                $testUser = App\Models\User::create([
                    'nim' => 'TEST' . strtoupper($status),
                    'name' => 'Test ' . ucfirst($status),
                    'email' => "test_{$status}@example.com",
                    'password' => Hash::make('password'),
                    'phone' => '081234567890',
                    'gender' => 'male',
                    'faculty' => 'Test Faculty',
                    'major' => 'Test Major',
                    'batch' => '2024',
                    'role' => 'student',
                    'status' => $status,
                ]);

                $testResults[$status] = '✅ Success';
                $testUser->delete(); // Clean up

            } catch (Exception $e) {
                $testResults[$status] = '❌ Failed: ' . $e->getMessage();
            }
        }
        $results['status_creation_tests'] = $testResults;

        // 4. Check current users by status
        $statusDistribution = DB::table('users')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');
        $results['current_status_distribution'] = $statusDistribution;

        // 5. Check registration controller
        $registrationFile = file_get_contents(app_path('Http/Controllers/Auth/RegisteredUserController.php'));
        $results['registration_uses_pending'] = strpos($registrationFile, "'status' => 'pending'") !== false;

        return response()->json([
            'success' => true,
            'message' => 'Status column verification completed!',
            'verification_results' => $results,
            'summary' => [
                'status_enum_updated' => $results['pending_allowed'] && $results['suspended_allowed'],
                'registration_compatible' => $results['pending_allowed'] && $results['registration_uses_pending'],
                'all_status_values_work' => !in_array('❌', array_values($testResults)),
                'ready_for_registration' => true
            ],
            'next_steps' => [
                'Try registering a new user',
                'Test admin user management',
                'Verify status-based access control'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});
