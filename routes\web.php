<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UkmController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;


// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');
Route::get('/search', [HomeController::class, 'search'])->name('search');

// UKM Routes
Route::get('/ukm', [UkmController::class, 'index'])->name('ukms.index');
Route::get('/ukm/{ukm}', [UkmController::class, 'show'])->name('ukms.show');

// Event Routes
Route::get('/kegiatan', [EventController::class, 'index'])->name('events.index');
Route::get('/kegiatan/{event}', [EventController::class, 'show'])->name('events.show');

// Include Authentication Routes
require __DIR__.'/auth.php';

// Backup Registration Success Route (in case auth.php doesn't load)
Route::get('/register/success', function() {
    return view('auth.register-success');
})->name('register.success.backup');

// Protected Routes
Route::middleware(['auth', 'check.status'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // UKM Membership
    Route::get('/ukm/{ukm}/daftar', [UkmController::class, 'showRegistrationForm'])->name('ukms.registration-form');
    Route::post('/ukm/{ukm}/daftar', [UkmController::class, 'submitRegistration'])->name('ukms.submit-registration');
    Route::post('/ukm/{ukm}/gabung', [UkmController::class, 'join'])->name('ukms.join');
    Route::delete('/ukm/{ukm}/keluar', [UkmController::class, 'leave'])->name('ukms.leave');

    // Event Registration
    Route::get('/events/{event}/register', [EventController::class, 'showRegistrationForm'])->name('events.register-form');
    Route::post('/events/{event}/register', [EventController::class, 'register'])->name('events.register');
    Route::delete('/events/{event}/cancel', [EventController::class, 'cancelRegistration'])->name('events.cancel');

    // Event Attendance
    Route::get('/events/{event}/attendance', [App\Http\Controllers\AttendanceController::class, 'showForm'])->name('events.attendance.form');
    Route::post('/events/{event}/attendance', [App\Http\Controllers\AttendanceController::class, 'submit'])->name('events.attendance.submit');
    Route::get('/events/{event}/certificate', [App\Http\Controllers\AttendanceController::class, 'downloadCertificate'])->name('events.attendance.certificate');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin routes that require authentication and admin role
    Route::middleware(['auth', 'check.status', 'admin'])->group(function () {
        // Dashboard
        Route::get('/', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard.alt');

        // User Management
        Route::resource('users', App\Http\Controllers\Admin\UserManagementController::class);
        Route::patch('users/{user}/activate', [App\Http\Controllers\Admin\UserManagementController::class, 'activate'])->name('users.activate');
        Route::patch('users/{user}/suspend', [App\Http\Controllers\Admin\UserManagementController::class, 'suspend'])->name('users.suspend');

        // UKM Management
        Route::resource('ukms', App\Http\Controllers\Admin\UkmManagementController::class);
        Route::delete('ukms/{ukm}/remove-leader', [App\Http\Controllers\Admin\UkmManagementController::class, 'removeLeader'])->name('ukms.remove-leader');

        // Ketua UKM Management
        Route::resource('ketua-ukm', App\Http\Controllers\Admin\KetuaUkmManagementController::class);
        Route::post('ketua-ukm/{ketuaUkm}/assign-ukm', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'assignUkm'])->name('ketua-ukm.assign-ukm');
        Route::delete('ketua-ukm/{ketuaUkm}/remove-ukm/{ukm}', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'removeUkm'])->name('ketua-ukm.remove-ukm');
        Route::patch('ketua-ukm/{ketuaUkm}/suspend', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'suspend'])->name('ketua-ukm.suspend');
        Route::patch('ketua-ukm/{ketuaUkm}/activate', [App\Http\Controllers\Admin\KetuaUkmManagementController::class, 'activate'])->name('ketua-ukm.activate');

        // Event Management
        Route::resource('events', App\Http\Controllers\Admin\EventManagementController::class);
        Route::patch('events/{event}/publish', [App\Http\Controllers\Admin\EventManagementController::class, 'publish'])->name('events.publish');
        Route::patch('events/{event}/cancel', [App\Http\Controllers\Admin\EventManagementController::class, 'cancel'])->name('events.cancel');
        Route::patch('events/{event}/approve', [App\Http\Controllers\Admin\EventManagementController::class, 'approve'])->name('events.approve');
        Route::patch('events/{event}/reject', [App\Http\Controllers\Admin\EventManagementController::class, 'reject'])->name('events.reject');
        Route::patch('events/{event}/cancel-event', [App\Http\Controllers\Admin\EventManagementController::class, 'cancelEvent'])->name('events.cancel-event');
        Route::post('events/update-statuses', [App\Http\Controllers\Admin\EventManagementController::class, 'updateAllStatuses'])->name('events.update-statuses');

        // Additional admin routes
        Route::get('/stats', [App\Http\Controllers\Admin\AdminController::class, 'stats'])->name('stats');
    });
});

// Ketua UKM Routes
Route::prefix('ketua-ukm')->name('ketua-ukm.')->middleware(['auth', 'check.status'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\KetuaUkmController::class, 'dashboard'])->name('dashboard');

    // UKM Management
    Route::get('/manage/{id}', [App\Http\Controllers\KetuaUkmController::class, 'manageUkm'])->name('manage');
    Route::get('/edit-ukm/{id}', [App\Http\Controllers\KetuaUkmController::class, 'editUkm'])->name('edit-ukm');
    Route::put('/update-ukm/{id}', [App\Http\Controllers\KetuaUkmController::class, 'updateUkm'])->name('update-ukm');
    Route::get('/ukm/edit', [App\Http\Controllers\KetuaUkmController::class, 'editUkm'])->name('ukm.edit');
    Route::put('/ukm/update', [App\Http\Controllers\KetuaUkmController::class, 'updateUkm'])->name('ukm.update');

    // Event Management
    Route::get('/events', [App\Http\Controllers\KetuaUkmController::class, 'events'])->name('events');
    Route::get('/events/create', [App\Http\Controllers\KetuaUkmController::class, 'createEvent'])->name('events.create');
    Route::post('/events', [App\Http\Controllers\KetuaUkmController::class, 'storeEvent'])->name('events.store');
    Route::get('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'showEvent'])->name('events.show');
    Route::get('/events/{event}/edit', [App\Http\Controllers\KetuaUkmController::class, 'editEvent'])->name('events.edit');
    Route::put('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'updateEvent'])->name('events.update');
    Route::delete('/events/{event}', [App\Http\Controllers\KetuaUkmController::class, 'destroyEvent'])->name('events.destroy');
    Route::get('/create-event/{ukmId?}', [App\Http\Controllers\KetuaUkmController::class, 'createEvent'])->name('create-event');
    Route::post('/store-event', [App\Http\Controllers\KetuaUkmController::class, 'storeEvent'])->name('store-event');

    // Event Attendances
    Route::get('/events/{event}/attendances', [App\Http\Controllers\KetuaUkmController::class, 'showAttendances'])->name('events.attendances');
    Route::post('/events/{event}/attendances/{attendance}/verify', [App\Http\Controllers\KetuaUkmController::class, 'verifyAttendance'])->name('events.attendances.verify');

    // Event Registrations Management
    Route::get('/events/{event}/registrations', [App\Http\Controllers\KetuaUkmController::class, 'showEventRegistrations'])->name('events.registrations');
    Route::post('/events/{event}/registrations/{registration}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveEventRegistration'])->name('events.registrations.approve');
    Route::post('/events/{event}/registrations/{registration}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectEventRegistration'])->name('events.registrations.reject');
    Route::post('/events/{event}/registrations/bulk-approve', [App\Http\Controllers\KetuaUkmController::class, 'bulkApproveEventRegistrations'])->name('events.registrations.bulk-approve');

    // Member Management
    Route::get('/pending-members', [App\Http\Controllers\KetuaUkmController::class, 'pendingMembers'])->name('pending-members');
    Route::get('/pending-members/{member}/details', [App\Http\Controllers\KetuaUkmController::class, 'getMemberDetails'])->name('pending-members.details');
    Route::put('/pending-members/{member}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveMember'])->name('pending-members.approve');
    Route::put('/pending-members/{member}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectMember'])->name('pending-members.reject');

    Route::get('/members', [App\Http\Controllers\KetuaUkmController::class, 'members'])->name('members');
    Route::get('/members/{member}/details', [App\Http\Controllers\KetuaUkmController::class, 'getMemberDetails'])->name('members.details');
    Route::put('/members/{member}/approve', [App\Http\Controllers\KetuaUkmController::class, 'approveMember'])->name('members.approve');
    Route::put('/members/{member}/reject', [App\Http\Controllers\KetuaUkmController::class, 'rejectMember'])->name('members.reject');
    Route::delete('/members/{member}/remove', [App\Http\Controllers\KetuaUkmController::class, 'removeMember'])->name('members.remove');
});

// Notification Routes
Route::middleware(['auth', 'check.status'])->group(function () {
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('/notifications/unread-count', [App\Http\Controllers\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::get('/notifications/recent', [App\Http\Controllers\NotificationController::class, 'getRecent'])->name('notifications.recent');
});
