<?php $__env->startSection('title', '<PERSON> - ' . $user->name); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Mahasiswa</h1>
                <p class="text-gray-600 mt-2">Ubah informasi data mahasiswa</p>
            </div>
            <a href="<?php echo e(route('admin.users.show', $user->id)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Ke<PERSON><PERSON> ke Detail
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-lg">
        <form action="<?php echo e(route('admin.users.update', $user->id)); ?>" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

            <!-- Form Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Informasi Mahasiswa</h2>
            </div>

            <div class="p-6 space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- NIM -->
                    <div>
                        <label for="nim" class="form-label">NIM *</label>
                        <input type="text" id="nim" name="nim" required
                               value="<?php echo e(old('nim', $user->nim)); ?>"
                               class="form-input <?php $__errorArgs = ['nim'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Nomor Induk Mahasiswa">
                        <?php $__errorArgs = ['nim'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Name -->
                    <div>
                        <label for="name" class="form-label">Nama Lengkap *</label>
                        <input type="text" id="name" name="name" required
                               value="<?php echo e(old('name', $user->name)); ?>"
                               class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Nama lengkap mahasiswa">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo e(old('email', $user->email)); ?>"
                               class="form-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="<EMAIL>">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="form-label">Nomor Telepon *</label>
                        <input type="text" id="phone" name="phone" required
                               value="<?php echo e(old('phone', $user->phone)); ?>"
                               class="form-input <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="081234567890">
                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Gender -->
                    <div>
                        <label for="gender" class="form-label">Jenis Kelamin *</label>
                        <select id="gender" name="gender" required
                                class="form-input <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="male" <?php echo e(old('gender', $user->gender) == 'male' ? 'selected' : ''); ?>>Laki-laki</option>
                            <option value="female" <?php echo e(old('gender', $user->gender) == 'female' ? 'selected' : ''); ?>>Perempuan</option>
                        </select>
                        <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Faculty -->
                    <div>
                        <label for="faculty" class="form-label">Fakultas *</label>
                        <select id="faculty" name="faculty" required
                                class="form-input <?php $__errorArgs = ['faculty'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Pilih Fakultas</option>
                            <option value="Fakultas Informatika" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Informatika' ? 'selected' : ''); ?>>Fakultas Informatika</option>
                            <option value="Fakultas Teknik Elektro" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Teknik Elektro' ? 'selected' : ''); ?>>Fakultas Teknik Elektro</option>
                            <option value="Fakultas Rekayasa Industri" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Rekayasa Industri' ? 'selected' : ''); ?>>Fakultas Rekayasa Industri</option>
                            <option value="Fakultas Ekonomi dan Bisnis" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Ekonomi dan Bisnis' ? 'selected' : ''); ?>>Fakultas Ekonomi dan Bisnis</option>
                            <option value="Fakultas Komunikasi dan Bisnis" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Komunikasi dan Bisnis' ? 'selected' : ''); ?>>Fakultas Komunikasi dan Bisnis</option>
                            <option value="Fakultas Industri Kreatif" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Industri Kreatif' ? 'selected' : ''); ?>>Fakultas Industri Kreatif</option>
                            <option value="Fakultas Ilmu Terapan" <?php echo e(old('faculty', $user->faculty) == 'Fakultas Ilmu Terapan' ? 'selected' : ''); ?>>Fakultas Ilmu Terapan</option>
                        </select>
                        <?php $__errorArgs = ['faculty'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Major -->
                    <div>
                        <label for="major" class="form-label">Program Studi *</label>
                        <input type="text" id="major" name="major" required
                               value="<?php echo e(old('major', $user->major)); ?>"
                               class="form-input <?php $__errorArgs = ['major'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Sistem Informasi">
                        <?php $__errorArgs = ['major'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Batch -->
                    <div>
                        <label for="batch" class="form-label">Angkatan *</label>
                        <select id="batch" name="batch" required
                                class="form-input <?php $__errorArgs = ['batch'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Pilih Angkatan</option>
                            <?php for($year = date('Y'); $year >= 2018; $year--): ?>
                                <option value="<?php echo e($year); ?>" <?php echo e(old('batch', $user->batch) == $year ? 'selected' : ''); ?>><?php echo e($year); ?></option>
                            <?php endfor; ?>
                        </select>
                        <?php $__errorArgs = ['batch'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Role -->
                    <div>
                        <label for="role" class="form-label">Role *</label>
                        <select id="role" name="role" required
                                class="form-input <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="student" <?php echo e(old('role', $user->role) == 'student' ? 'selected' : ''); ?>>Mahasiswa</option>
                            <option value="ketua_ukm" <?php echo e(old('role', $user->role) == 'ketua_ukm' ? 'selected' : ''); ?>>Ketua UKM</option>
                            <option value="admin" <?php echo e(old('role', $user->role) == 'admin' ? 'selected' : ''); ?>>Admin</option>
                        </select>
                        <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-sm text-gray-500">
                            <strong>Mahasiswa:</strong> Akses dashboard mahasiswa, join UKM, daftar event<br>
                            <strong>Ketua UKM:</strong> Dapat ditugaskan sebagai ketua UKM untuk mengelola UKM<br>
                            <strong>Admin:</strong> Akses penuh ke admin panel
                        </p>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="form-label">Status *</label>
                        <select id="status" name="status" required
                                class="form-input <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="pending" <?php echo e(old('status', $user->status) == 'pending' ? 'selected' : ''); ?>>Menunggu Persetujuan</option>
                            <option value="active" <?php echo e(old('status', $user->status) == 'active' ? 'selected' : ''); ?>>Aktif</option>
                            <option value="inactive" <?php echo e(old('status', $user->status) == 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                            <option value="suspended" <?php echo e(old('status', $user->status) == 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                            <option value="graduated" <?php echo e(old('status', $user->status) == 'graduated' ? 'selected' : ''); ?>>Lulus</option>
                        </select>
                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Profile Photo -->
                <div>
                    <label for="avatar" class="form-label">Foto Profil</label>
                    <div class="flex items-center space-x-6">
                        <div class="shrink-0">
                            <?php if($user->avatar): ?>
                                <img class="h-16 w-16 object-cover rounded-full"
                                     src="<?php echo e(asset('storage/' . $user->avatar)); ?>"
                                     alt="<?php echo e($user->name); ?>">
                            <?php else: ?>
                                <div class="h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <input type="file" id="avatar" name="avatar"
                                   accept="image/*"
                                   class="form-input <?php $__errorArgs = ['avatar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <p class="mt-1 text-sm text-gray-500">JPG, PNG, atau GIF. Maksimal 2MB.</p>
                            <?php $__errorArgs = ['avatar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Password Reset -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Reset Password</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="password" class="form-label">Password Baru</label>
                            <input type="password" id="password" name="password"
                                   class="form-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   placeholder="Kosongkan jika tidak ingin mengubah">
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="password_confirmation" class="form-label">Konfirmasi Password</label>
                            <input type="password" id="password_confirmation" name="password_confirmation"
                                   class="form-input"
                                   placeholder="Ulangi password baru">
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        Kosongkan field password jika tidak ingin mengubah password mahasiswa.
                    </p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('admin.users.show', $user->id)); ?>" class="btn-secondary">
                        Batal
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Perubahan
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-generate email based on NIM
document.getElementById('nim').addEventListener('input', function() {
    const nim = this.value;
    const emailField = document.getElementById('email');

    if (nim && !emailField.value.includes('@')) {
        emailField.value = nim.toLowerCase() + '@student.telkomuniversity.ac.id';
    }
});

// Faculty-Major mapping
const facultyMajorMap = {
    'Fakultas Informatika': ['Sistem Informasi', 'Teknik Informatika', 'Teknologi Informasi', 'Sains Data'],
    'Fakultas Teknik Elektro': ['Teknik Elektro', 'Teknik Telekomunikasi', 'Teknik Fisika', 'Teknik Biomedis'],
    'Fakultas Rekayasa Industri': ['Teknik Industri', 'Teknik Logistik', 'Sistem Informasi Bisnis'],
    'Fakultas Ekonomi dan Bisnis': ['Manajemen', 'Akuntansi', 'Ekonomi Pembangunan'],
    'Fakultas Komunikasi dan Bisnis': ['Ilmu Komunikasi', 'Administrasi Bisnis', 'Digital Marketing'],
    'Fakultas Industri Kreatif': ['Desain Komunikasi Visual', 'Desain Produk', 'Kriya Tekstil'],
    'Fakultas Ilmu Terapan': ['Teknologi Pangan', 'Rekayasa Perangkat Lunak', 'Teknologi Informasi']
};

document.getElementById('faculty').addEventListener('change', function() {
    const faculty = this.value;
    const majorField = document.getElementById('major');

    if (faculty && facultyMajorMap[faculty]) {
        // Convert input to datalist for suggestions
        majorField.setAttribute('list', 'major-options');

        // Create or update datalist
        let datalist = document.getElementById('major-options');
        if (!datalist) {
            datalist = document.createElement('datalist');
            datalist.id = 'major-options';
            majorField.parentNode.appendChild(datalist);
        }

        datalist.innerHTML = '';
        facultyMajorMap[faculty].forEach(major => {
            const option = document.createElement('option');
            option.value = major;
            datalist.appendChild(option);
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\resources\views/admin/users/edit.blade.php ENDPATH**/ ?>