@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>ftaran - ' . $event->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Kelola Pendaftaran Event</h1>
                <p class="text-gray-600">{{ $event->title }}</p>
            </div>
            <a href="{{ route('ketua-ukm.events') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Kembali ke Daftar Event
            </a>
        </div>
    </div>

    <!-- Event Info Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Informasi Event</h3>
                <div class="space-y-1 text-sm text-gray-600">
                    <p><i class="fas fa-calendar mr-2"></i>{{ $event->start_datetime->format('d M Y, H:i') }} - {{ $event->end_datetime->format('d M Y, H:i') }}</p>
                    <p><i class="fas fa-map-marker-alt mr-2"></i>{{ $event->location }}</p>
                    <p><i class="fas fa-users mr-2"></i>{{ $event->max_participants ? $event->current_participants . '/' . $event->max_participants : $event->current_participants }} peserta</p>
                    <p><i class="fas fa-cog mr-2"></i>{{ $event->requires_approval ? 'Perlu Persetujuan' : 'Auto Approve' }}</p>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Status Pendaftaran</h3>
                <div class="space-y-1 text-sm">
                    @php
                        $totalRegistrations = $registrations->total();
                        $pendingCount = $registrations->where('status', 'pending')->count();
                        $approvedCount = $registrations->where('status', 'approved')->count();
                        $rejectedCount = $registrations->where('status', 'rejected')->count();
                    @endphp
                    <p class="text-yellow-600"><i class="fas fa-clock mr-2"></i>{{ $pendingCount }} menunggu persetujuan</p>
                    <p class="text-green-600"><i class="fas fa-check mr-2"></i>{{ $approvedCount }} disetujui</p>
                    <p class="text-red-600"><i class="fas fa-times mr-2"></i>{{ $rejectedCount }} ditolak</p>
                    <p class="text-gray-600"><i class="fas fa-list mr-2"></i>{{ $totalRegistrations }} total pendaftar</p>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Aksi Cepat</h3>
                <div class="space-y-2">
                    <button onclick="openBulkModal()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-check-double mr-2"></i>Approve Massal
                    </button>
                    <a href="{{ route('events.show', $event->slug) }}" target="_blank" class="block w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>Lihat Event
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <form method="GET" action="{{ route('ketua-ukm.events.registrations', $event) }}" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-48">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Pendaftar</label>
                <input type="text" id="search" name="search" value="{{ request('search') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Nama atau NIM mahasiswa...">
            </div>
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status Pendaftaran</label>
                <select id="status" name="status" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Semua Status</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Menunggu</option>
                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                    <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                </select>
            </div>
            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="{{ route('ketua-ukm.events.registrations', $event) }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Reset
                </a>
            </div>
        </form>
    </div>

    <!-- Registrations List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Daftar Pendaftar Event</h2>
                <div class="flex items-center gap-2">
                    <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="selectAll" class="text-sm text-gray-700">Pilih Semua</label>
                </div>
            </div>
        </div>

        @if($registrations->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pendaftar</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motivasi</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Waktu Daftar</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($registrations as $registration)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <input type="checkbox" name="registration_ids[]" value="{{ $registration->id }}" 
                                           class="registration-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">
                                                    {{ substr($registration->user->name, 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $registration->user->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $registration->user->student_id ?? 'N/A' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs">
                                        <p class="truncate">{{ Str::limit($registration->motivation, 100) }}</p>
                                        @if(strlen($registration->motivation) > 100)
                                            <button onclick="viewMotivation('{{ $registration->user->name }}', '{{ addslashes($registration->motivation) }}')" 
                                                    class="text-blue-600 hover:text-blue-800 text-xs">
                                                Lihat selengkapnya
                                            </button>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $registration->created_at->format('d M Y, H:i') }}
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    @if($registration->status === 'pending')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock mr-1"></i>Menunggu
                                        </span>
                                    @elseif($registration->status === 'approved')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>Disetujui
                                        </span>
                                    @elseif($registration->status === 'rejected')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times mr-1"></i>Ditolak
                                        </span>
                                    @endif
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    @if($registration->status === 'pending')
                                        <div class="flex gap-2">
                                            <button onclick="approveRegistration({{ $registration->id }})" 
                                                    class="text-green-600 hover:text-green-800" title="Setujui">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button onclick="rejectRegistration({{ $registration->id }})" 
                                                    class="text-red-600 hover:text-red-800" title="Tolak">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <button onclick="viewDetails({{ $registration->id }})" 
                                                    class="text-blue-600 hover:text-blue-800" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    @else
                                        <button onclick="viewDetails({{ $registration->id }})" 
                                                class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-eye mr-1"></i>Detail
                                        </button>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-4 py-3 border-t border-gray-200">
                {{ $registrations->links() }}
            </div>
        @else
            <div class="p-8 text-center">
                <i class="fas fa-user-plus text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Pendaftar</h3>
                <p class="text-gray-500">Belum ada mahasiswa yang mendaftar untuk event ini.</p>
            </div>
        @endif
    </div>
</div>

<!-- Motivation Modal -->
<div id="motivationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="motivationModalTitle">Motivasi Pendaftar</h3>
            <div class="text-sm text-gray-700" id="motivationContent"></div>
            <div class="flex justify-end mt-4">
                <button onclick="closeMotivationModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <form id="approvalForm" method="POST">
            @csrf
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4" id="approvalModalTitle">Konfirmasi Aksi</h3>
                <p class="text-sm text-gray-600 mb-4" id="approvalModalMessage"></p>
                <div class="mb-4">
                    <label for="approval_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Catatan (Opsional)
                    </label>
                    <textarea id="approval_notes" name="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Tambahkan catatan..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeApprovalModal()" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                        Batal
                    </button>
                    <button type="submit" id="approvalSubmitBtn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        Konfirmasi
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// View motivation
function viewMotivation(userName, motivation) {
    document.getElementById('motivationModalTitle').textContent = `Motivasi - ${userName}`;
    document.getElementById('motivationContent').textContent = motivation;
    document.getElementById('motivationModal').classList.remove('hidden');
}

function closeMotivationModal() {
    document.getElementById('motivationModal').classList.add('hidden');
}

// Approve registration
function approveRegistration(registrationId) {
    if (confirm('Apakah Anda yakin ingin menyetujui pendaftaran ini?')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/ketua-ukm/events/{{ $event->id }}/registrations/${registrationId}/approve`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

// Reject registration
function rejectRegistration(registrationId) {
    const reason = prompt('Alasan penolakan (opsional):');
    if (reason !== null) { // User didn't cancel
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/ketua-ukm/events/{{ $event->id }}/registrations/${registrationId}/reject`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        if (reason.trim()) {
            const notesInput = document.createElement('input');
            notesInput.type = 'hidden';
            notesInput.name = 'notes';
            notesInput.value = reason;
            form.appendChild(notesInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

function closeApprovalModal() {
    document.getElementById('approvalModal').classList.add('hidden');
    document.getElementById('approval_notes').value = '';
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.registration-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// View details (placeholder)
function viewDetails(registrationId) {
    alert('Detail view akan diimplementasikan');
}

// Bulk approval
function openBulkModal() {
    const selectedCheckboxes = document.querySelectorAll('.registration-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('Pilih minimal satu pendaftaran untuk approve massal');
        return;
    }

    if (confirm(`Apakah Anda yakin ingin menyetujui ${selectedCheckboxes.length} pendaftaran sekaligus?`)) {
        // Create form for bulk approval
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/ketua-ukm/events/{{ $event->id }}/registrations/bulk-approve`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add selected registration IDs
        selectedCheckboxes.forEach(checkbox => {
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'registration_ids[]';
            idInput.value = checkbox.value;
            form.appendChild(idInput);
        });

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection
