<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Event extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'ukm_id',
        'title',
        'slug',
        'description',
        'requirements',
        'poster',
        'gallery',
        'type',
        'location',
        'start_datetime',
        'end_datetime',
        'registration_start',
        'registration_end',
        'max_participants',
        'current_participants',
        'registration_fee',
        'status',
        'requires_approval',
        'registration_open',
        'certificate_available',
        'certificate_template',
        'contact_person',
        'notes',
        'proposal_file',
        'rab_file',
        'lpj_file',
    ];

    protected function casts(): array
    {
        return [
            'gallery' => 'array',
            'contact_person' => 'array',
            'start_datetime' => 'datetime',
            'end_datetime' => 'datetime',
            'registration_start' => 'datetime',
            'registration_end' => 'datetime',
            'approved_at' => 'datetime',
            'registration_fee' => 'decimal:2',
            'requires_approval' => 'boolean',
            'registration_open' => 'boolean',
            'certificate_available' => 'boolean',
        ];
    }

    protected $attributes = [
        'requires_approval' => true,
        'registration_open' => true,
        'certificate_available' => false,
        'current_participants' => 0,
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the UKM that organizes this event.
     */
    public function ukm()
    {
        return $this->belongsTo(Ukm::class);
    }

    /**
     * Get the registrations for this event.
     */
    public function registrations()
    {
        return $this->hasMany(EventRegistration::class);
    }

    public function attendances()
    {
        return $this->hasMany(EventAttendance::class);
    }

    /**
     * Check if event has ended and attendance can be submitted
     */
    public function canSubmitAttendance(): bool
    {
        return $this->end_datetime < now();
    }

    /**
     * Create attendance records for all approved registrations
     */
    public function createAttendanceRecords()
    {
        $approvedRegistrations = $this->registrations()->where('status', 'approved')->get();

        foreach ($approvedRegistrations as $registration) {
            EventAttendance::firstOrCreate([
                'event_id' => $this->id,
                'user_id' => $registration->user_id,
                'event_registration_id' => $registration->id,
            ]);
        }
    }

    /**
     * Get approved registrations only.
     */
    public function approvedRegistrations()
    {
        return $this->registrations()->where('status', 'approved');
    }



    /**
     * Get the certificates issued for this event.
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Get the admin who approved this event.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if registration is open.
     */
    public function isRegistrationOpen(): bool
    {
        $now = now();
        return $this->status === 'published' &&
               ($this->registration_start === null || $now >= $this->registration_start) &&
               ($this->registration_end === null || $now <= $this->registration_end) &&
               ($this->max_participants === null || $this->current_participants < $this->max_participants);
    }

    /**
     * Check if event is ongoing.
     */
    public function isOngoing(): bool
    {
        $now = now();
        return $now >= $this->start_datetime && $now <= $this->end_datetime;
    }

    /**
     * Check if event is completed.
     */
    public function isCompleted(): bool
    {
        return now() > $this->end_datetime;
    }

    /**
     * Update participant count.
     */
    public function updateParticipantCount()
    {
        $this->current_participants = $this->approvedRegistrations()->count();
        $this->save();
    }

    /**
     * Scope for published events.
     */
    public function scopePublished($query)
    {
        return $query->where('events.status', 'published');
    }

    /**
     * Scope for upcoming events.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_datetime', '>', now());
    }

    /**
     * Scope for ongoing events.
     */
    public function scopeOngoing($query)
    {
        return $query->where('start_datetime', '<=', now())
                    ->where('end_datetime', '>=', now());
    }

    /**
     * Scope by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
