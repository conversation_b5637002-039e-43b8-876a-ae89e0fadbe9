@extends('admin.layouts.app')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
            <p class="text-gray-600">Manajemen data mahasiswa dan akun pengguna</p>
            <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>💡 <strong>Tips:</strong> Gunakan tombol Aktifkan/Suspend untuk mengubah status akun</span>
            </div>
        </div>
        <a href="{{ route('admin.users.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <PERSON><PERSON> Mahasiswa
        </a>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="GET" action="{{ route('admin.users.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="form-label">Pencarian</label>
                <input type="text" name="search" value="{{ request('search') }}"
                       placeholder="Nama, NIM, Email, Jurusan..."
                       class="form-input">
            </div>
            <div>
                <label class="form-label">Fakultas</label>
                <select name="faculty" class="form-input">
                    <option value="">Semua Fakultas</option>
                    @foreach($faculties as $faculty)
                        <option value="{{ $faculty }}" {{ request('faculty') == $faculty ? 'selected' : '' }}>
                            {{ $faculty }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="form-label">Status</label>
                <select name="status" class="form-input">
                    <option value="">Semua Status</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
                    <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>Suspended</option>
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="btn-primary">Filter</button>
                <a href="{{ route('admin.users.index') }}" class="btn-secondary">Reset</a>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
                Daftar Mahasiswa ({{ $users->total() }} total)
            </h3>
        </div>

        @if($users->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Mahasiswa
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                NIM
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Fakultas/Jurusan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Terdaftar
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($users as $user)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">
                                                    {{ substr($user->name, 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $user->nim }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $user->faculty }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->major }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($user->status === 'active')
                                        <span class="badge badge-success">Aktif</span>
                                    @elseif($user->status === 'pending')
                                        <span class="badge badge-warning">Menunggu Persetujuan</span>
                                    @elseif($user->status === 'inactive')
                                        <span class="badge badge-secondary">Tidak Aktif</span>
                                    @elseif($user->status === 'suspended')
                                        <span class="badge badge-danger">Suspended</span>
                                    @elseif($user->status === 'graduated')
                                        <span class="badge badge-info">Lulus</span>
                                    @else
                                        <span class="badge badge-secondary">{{ ucfirst($user->status) }}</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $user->created_at->format('d M Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <!-- Quick Actions for Status -->
                                        @if($user->status === 'pending')
                                            <form action="{{ route('admin.users.activate', $user) }}"
                                                  method="POST" class="inline"
                                                  onsubmit="return confirm('Aktifkan akun {{ $user->name }}?')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium shadow-sm">
                                                    Aktifkan
                                                </button>
                                            </form>
                                        @elseif($user->status === 'suspended')
                                            <form action="{{ route('admin.users.activate', $user) }}"
                                                  method="POST" class="inline"
                                                  onsubmit="return confirm('Aktifkan kembali akun {{ $user->name }}?')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium shadow-sm">
                                                    Aktifkan
                                                </button>
                                            </form>
                                        @elseif($user->status === 'inactive')
                                            <form action="{{ route('admin.users.activate', $user) }}"
                                                  method="POST" class="inline"
                                                  onsubmit="return confirm('Aktifkan akun {{ $user->name }}?')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium shadow-sm">
                                                    Aktifkan
                                                </button>
                                            </form>
                                        @elseif($user->status === 'active')
                                            <form action="{{ route('admin.users.suspend', $user) }}"
                                                  method="POST" class="inline"
                                                  onsubmit="return confirm('Suspend akun {{ $user->name }}?')">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium">
                                                    Suspend
                                                </button>
                                            </form>
                                        @endif

                                        <!-- Regular Actions -->
                                        <a href="{{ route('admin.users.show', $user) }}"
                                           class="text-blue-600 hover:text-blue-900">
                                            Lihat
                                        </a>
                                        <a href="{{ route('admin.users.edit', $user) }}"
                                           class="text-indigo-600 hover:text-indigo-900">
                                            Edit
                                        </a>
                                        @if($user->role !== 'admin')
                                            <form action="{{ route('admin.users.destroy', $user) }}"
                                                  method="POST" class="inline"
                                                  onsubmit="return confirm('Yakin ingin menghapus mahasiswa ini?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900">
                                                    Hapus
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $users->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada mahasiswa</h3>
                <p class="mt-1 text-sm text-gray-500">Mulai dengan menambahkan mahasiswa baru.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.users.create') }}" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Mahasiswa
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

@if(session('success'))
    <div class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg">
        {{ session('error') }}
    </div>
@endif
@endsection
