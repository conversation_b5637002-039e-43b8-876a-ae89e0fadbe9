<?php $__env->startSection('title', '- Beranda'); ?>
<?php $__env->startSection('description', 'Website resmi Unit Kegiatan Mahasiswa Telkom Jakarta. Temukan UKM yang sesuai dengan minat dan bakat <PERSON>, ikuti berbagai kegiatan menarik untuk mengembangkan potensi diri.'); ?>
<?php $__env->startSection('keywords', 'UKM Telkom Jakarta, mahasiswa, kegiatan, organisasi, ekstrakurikuler, pengembangan diri'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-telkom-600 text-white overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl lg:text-6xl font-display font-bold mb-6 leading-tight">
                    Bergabung dengan
                    <span class="text-yellow-300">UKM Telkom Jakarta</span>
                </h1>
                <p class="text-xl lg:text-2xl mb-8 text-gray-100 leading-relaxed">
                    Kembangkan potensi diri, perluas jaringan, dan raih prestasi bersama komunitas mahasiswa yang aktif dan kreatif.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="<?php echo e(route('ukms.index')); ?>" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105">
                        Jelajahi UKM
                    </a>
                    <a href="<?php echo e(route('events.index')); ?>" class="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-4 rounded-xl font-semibold transition-all duration-300">
                        Lihat Kegiatan
                    </a>
                </div>
            </div>
            <div class="animate-slide-up">
                <img src="<?php echo e(asset('images/hero-illustration.svg')); ?>" alt="Students Activities" class="w-full h-auto">
            </div>
        </div>
    </div>
    
    <!-- Decorative Elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-yellow-300 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
    <div class="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center animate-bounce-in">
                <div class="text-4xl lg:text-5xl font-bold text-primary-600 mb-2"><?php echo e($stats['total_ukms'] ?? '25+'); ?></div>
                <div class="text-gray-600 font-medium">Unit Kegiatan Mahasiswa</div>
            </div>
            <div class="text-center animate-bounce-in" style="animation-delay: 0.1s;">
                <div class="text-4xl lg:text-5xl font-bold text-telkom-600 mb-2"><?php echo e($stats['total_members'] ?? '1000+'); ?></div>
                <div class="text-gray-600 font-medium">Anggota Aktif</div>
            </div>
            <div class="text-center animate-bounce-in" style="animation-delay: 0.2s;">
                <div class="text-4xl lg:text-5xl font-bold text-success-600 mb-2"><?php echo e($stats['total_events'] ?? '150+'); ?></div>
                <div class="text-gray-600 font-medium">Kegiatan per Tahun</div>
            </div>
            <div class="text-center animate-bounce-in" style="animation-delay: 0.3s;">
                <div class="text-4xl lg:text-5xl font-bold text-yellow-600 mb-2"><?php echo e($stats['total_achievements'] ?? '50+'); ?></div>
                <div class="text-gray-600 font-medium">Prestasi Diraih</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured UKMs Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-display font-bold text-gray-900 mb-4">
                UKM Unggulan
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Temukan UKM yang sesuai dengan minat dan bakat Anda. Bergabunglah dengan komunitas yang tepat untuk mengembangkan potensi diri.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $featured_ukms ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ukm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card hover:shadow-lg transition-shadow duration-300 animate-slide-up">
                    <div class="relative">
                        <img src="<?php echo e($ukm->banner ? Storage::url($ukm->banner) : asset('images/ukm-placeholder.jpg')); ?>" 
                             alt="<?php echo e($ukm->name); ?>" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="badge badge-info"><?php echo e(ucfirst($ukm->category)); ?></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($ukm->name); ?></h3>
                        <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e(Str::limit($ukm->description, 120)); ?></p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                <?php echo e($ukm->current_members); ?> anggota
                            </div>
                            <a href="<?php echo e(route('ukms.show', $ukm->slug)); ?>" class="btn-primary text-sm">
                                Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada UKM unggulan</h3>
                    <p class="text-gray-600">UKM unggulan akan ditampilkan di sini.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('ukms.index')); ?>" class="btn-primary text-lg px-8 py-4">
                Lihat Semua UKM
            </a>
        </div>
    </div>
</section>

<!-- Upcoming Events Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-display font-bold text-gray-900 mb-4">
                Kegiatan Mendatang
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Jangan lewatkan kegiatan menarik yang akan datang. Daftarkan diri Anda sekarang!
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $upcoming_events ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card hover:shadow-lg transition-shadow duration-300 animate-slide-up">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <img src="<?php echo e($event->poster ? Storage::url($event->poster) : asset('images/event-placeholder.jpg')); ?>" 
                                 alt="<?php echo e($event->title); ?>" class="w-24 h-24 object-cover rounded-lg">
                        </div>
                        <div class="ml-4 flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <span class="badge badge-info"><?php echo e(ucfirst($event->type)); ?></span>
                                <span class="text-sm text-gray-500"><?php echo e($event->ukm->name); ?></span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($event->title); ?></h3>
                            <div class="flex items-center text-sm text-gray-600 mb-2">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <?php echo e($event->start_datetime->format('d M Y, H:i')); ?>

                            </div>
                            <div class="flex items-center text-sm text-gray-600 mb-3">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php echo e($event->location); ?>

                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">
                                    <?php echo e($event->current_participants); ?>/<?php echo e($event->max_participants ?? '∞'); ?> peserta
                                </div>
                                <a href="<?php echo e(route('events.show', $event->slug)); ?>" class="btn-primary text-sm">
                                    Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada kegiatan mendatang</h3>
                    <p class="text-gray-600">Kegiatan mendatang akan ditampilkan di sini.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('events.index')); ?>" class="btn-primary text-lg px-8 py-4">
                Lihat Semua Kegiatan
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-primary-600 to-telkom-600 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl lg:text-4xl font-display font-bold mb-4">
            Siap Bergabung dengan Komunitas UKM?
        </h2>
        <p class="text-xl mb-8 max-w-3xl mx-auto">
            Daftarkan diri Anda sekarang dan mulai perjalanan pengembangan diri yang luar biasa bersama UKM Telkom Jakarta.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <?php if(auth()->guard()->guest()): ?>
                <a href="<?php echo e(route('register')); ?>" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 rounded-xl font-semibold">
                    Daftar Sekarang
                </a>
                <a href="<?php echo e(route('login')); ?>" class="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-4 rounded-xl font-semibold">
                    Masuk
                </a>
            <?php else: ?>
                <a href="<?php echo e(route('dashboard')); ?>" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 rounded-xl font-semibold">
                    Dashboard Saya
                </a>
                <a href="<?php echo e(route('ukms.index')); ?>" class="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-4 rounded-xl font-semibold">
                    Bergabung UKM
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\resources\views/home.blade.php ENDPATH**/ ?>