<?php $__env->startSection('title', 'Kelola Ketua UKM'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Kelola Ketua UKM</h1>
            <p class="text-gray-600"><PERSON><PERSON><PERSON> mahasiswa dengan role Ketua UKM</p>
        </div>
        <a href="<?php echo e(route('admin.ketua-ukm.create')); ?>" 
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-plus mr-2"></i>Angkat Ketua UKM
        </a>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <form method="GET" action="<?php echo e(route('admin.ketua-ukm.index')); ?>" class="flex flex-wrap gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                       placeholder="Cari nama, NIM, atau email..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Status Filter -->
            <div>
                <select name="status" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Semua Status</option>
                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Aktif</option>
                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                    <option value="suspended" <?php echo e(request('status') == 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                </select>
            </div>

            <!-- Buttons -->
            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-search mr-1"></i>Cari
                </button>
                <a href="<?php echo e(route('admin.ketua-ukm.index')); ?>" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-refresh mr-1"></i>Reset
                </a>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total Ketua UKM</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ketuaUkms->total()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-check-circle text-green-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Aktif</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ketuaUkms->where('status', 'active')->count()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-building text-yellow-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Memimpin UKM</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ketuaUkms->filter(function($user) { return $user->ledUkms->count() > 0; })->count()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <i class="fas fa-user-slash text-red-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Belum Ditugaskan</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ketuaUkms->filter(function($user) { return $user->ledUkms->count() == 0; })->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ketua UKM</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kontak</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UKM yang Dipimpin</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $ketuaUkms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ketuaUkm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <?php if($ketuaUkm->avatar): ?>
                                        <img class="h-10 w-10 rounded-full object-cover"
                                             src="<?php echo e(asset('storage/' . $ketuaUkm->avatar)); ?>"
                                             alt="<?php echo e($ketuaUkm->name); ?>">
                                    <?php else: ?>
                                        <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                <?php echo e(strtoupper(substr($ketuaUkm->name, 0, 2))); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($ketuaUkm->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($ketuaUkm->nim); ?></div>
                                    <div class="text-xs text-gray-400"><?php echo e($ketuaUkm->major); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900"><?php echo e($ketuaUkm->email); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($ketuaUkm->phone ?? '-'); ?></div>
                        </td>
                        <td class="px-6 py-4">
                            <?php if($ketuaUkm->ledUkms->count() > 0): ?>
                                <?php $__currentLoopData = $ketuaUkm->ledUkms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ukm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-1">
                                        <?php echo e($ukm->name); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="text-sm text-gray-400 italic">Belum ditugaskan</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if($ketuaUkm->status === 'active'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Aktif
                                </span>
                            <?php elseif($ketuaUkm->status === 'inactive'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-pause-circle mr-1"></i>Tidak Aktif
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-ban mr-1"></i>Suspended
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-3">
                                <!-- Status Actions -->
                                <?php if($ketuaUkm->status === 'suspended'): ?>
                                    <form action="<?php echo e(route('admin.ketua-ukm.activate', $ketuaUkm)); ?>"
                                          method="POST" class="inline"
                                          onsubmit="return confirm('Aktifkan kembali akun <?php echo e($ketuaUkm->name); ?>?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium shadow-sm">
                                            Aktifkan
                                        </button>
                                    </form>
                                <?php elseif($ketuaUkm->status === 'inactive'): ?>
                                    <form action="<?php echo e(route('admin.ketua-ukm.activate', $ketuaUkm)); ?>"
                                          method="POST" class="inline"
                                          onsubmit="return confirm('Aktifkan akun <?php echo e($ketuaUkm->name); ?>?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium shadow-sm">
                                            Aktifkan
                                        </button>
                                    </form>
                                <?php elseif($ketuaUkm->status === 'active'): ?>
                                    <form action="<?php echo e(route('admin.ketua-ukm.suspend', $ketuaUkm)); ?>"
                                          method="POST" class="inline"
                                          onsubmit="return confirm('Suspend akun <?php echo e($ketuaUkm->name); ?>?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium">
                                            Suspend
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <!-- Regular Actions -->
                                <a href="<?php echo e(route('admin.ketua-ukm.show', $ketuaUkm)); ?>"
                                   class="text-blue-600 hover:text-blue-900">
                                    Lihat
                                </a>
                                <a href="<?php echo e(route('admin.ketua-ukm.edit', $ketuaUkm)); ?>"
                                   class="text-indigo-600 hover:text-indigo-900">
                                    Edit
                                </a>
                                <form action="<?php echo e(route('admin.ketua-ukm.destroy', $ketuaUkm)); ?>"
                                      method="POST" class="inline"
                                      onsubmit="return confirm('Yakin ingin menurunkan <?php echo e($ketuaUkm->name); ?> dari ketua UKM? User akan dikembalikan ke role student.')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        Hapus
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="5" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-users text-4xl mb-4"></i>
                                <p class="text-lg font-medium">Belum ada ketua UKM</p>
                                <p class="text-sm">Angkat mahasiswa sebagai ketua UKM untuk memulai.</p>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($ketuaUkms->hasPages()): ?>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <?php echo e($ketuaUkms->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\TAWEBB\ukmwebLbasedfunc\resources\views/admin/ketua-ukm/index.blade.php ENDPATH**/ ?>